import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Blog - Wikify',
  description: 'Discover insightful articles, tutorials, and stories from our community of writers. Explore the latest posts on technology, programming, and more.',
  keywords: 'blog, articles, tutorials, technology, programming, community, writers',
  openGraph: {
    title: 'Blog - Wikify',
    description: 'Discover insightful articles, tutorials, and stories from our community of writers.',
    type: 'website',
    url: '/blog',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Blog - Wikify',
    description: 'Discover insightful articles, tutorials, and stories from our community of writers.',
  },
  alternates: {
    canonical: '/blog',
  },
};

interface BlogLayoutProps {
  children: React.ReactNode;
}

export default function BlogLayout({ children }: BlogLayoutProps) {
  return children;
}
