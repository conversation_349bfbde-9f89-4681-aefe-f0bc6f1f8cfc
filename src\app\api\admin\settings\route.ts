import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { options } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs/promises';
import path from 'path';

// Settings keys for database storage
const SETTINGS_KEYS = {
  SITE_SETTINGS: 'site_settings',
  EMAIL_SETTINGS: 'email_settings',
  SOCIAL_SETTINGS: 'social_settings',
  ADVANCED_SETTINGS: 'advanced_settings'
};

// Fallback file path for settings when database is not available
const SETTINGS_FILE = path.join(process.cwd(), 'data', 'settings.json');

// Default settings
const defaultSettings = {
  siteName: 'Wikify',
  siteDescription: 'How to Guide you can Trust',
  siteUrl: 'http://localhost:3000',
  adminEmail: '<EMAIL>',
  timezone: 'UTC',
  language: 'en',
  logo: '',
  favicon: '',
  allowRegistration: true,
  requireEmailVerification: false,
  defaultUserRole: 'AUTHOR',
  maintenanceMode: false,
  analyticsCode: '',
  socialLinks: {
    facebook: '',
    twitter: '',
    instagram: '',
    linkedin: ''
  },
  emailSettings: {
    smtpHost: '',
    smtpPort: '587',
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: ''
  }
};

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Load settings from file (fallback)
async function loadSettingsFromFile() {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
    return { ...defaultSettings, ...JSON.parse(data) };
  } catch (error) {
    return defaultSettings;
  }
}

// Save settings to file (fallback)
async function saveSettingsToFile(settings: any) {
  try {
    await ensureDataDirectory();
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error('Failed to save settings to file:', error);
    return false;
  }
}

// Load settings from database with file fallback
async function loadSettings() {
  try {
    // Try database first
    const settingsData = await db
      .select()
      .from(options)
      .where(eq(options.option_name, SETTINGS_KEYS.SITE_SETTINGS));

    if (settingsData.length > 0) {
      const savedSettings = JSON.parse(settingsData[0].option_value);
      return { ...defaultSettings, ...savedSettings };
    }

    return defaultSettings;
  } catch (error) {
    console.error('Failed to load settings from database, using file fallback:', error);
    // Fallback to file-based storage
    return await loadSettingsFromFile();
  }
}

// Save settings to database with file fallback
async function saveSettings(settings: any) {
  try {
    const settingsJson = JSON.stringify(settings);

    // Check if settings already exist
    const existingSettings = await db
      .select()
      .from(options)
      .where(eq(options.option_name, SETTINGS_KEYS.SITE_SETTINGS));

    if (existingSettings.length > 0) {
      // Update existing settings
      await db
        .update(options)
        .set({
          option_value: settingsJson,
          autoload: 'yes'
        })
        .where(eq(options.option_name, SETTINGS_KEYS.SITE_SETTINGS));
    } else {
      // Insert new settings
      await db
        .insert(options)
        .values({
          option_name: SETTINGS_KEYS.SITE_SETTINGS,
          option_value: settingsJson,
          autoload: 'yes'
        });
    }

    return true;
  } catch (error) {
    console.error('Failed to save settings to database, using file fallback:', error);
    // Fallback to file-based storage
    return await saveSettingsToFile(settings);
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const settings = await loadSettings();

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Settings GET error:', error);
    return NextResponse.json(
      { error: 'Failed to load settings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.siteName || !body.adminEmail) {
      return NextResponse.json(
        { error: 'Site name and admin email are required' },
        { status: 400 }
      );
    }

    // Merge with existing settings
    const currentSettings = await loadSettings();
    const updatedSettings = { ...currentSettings, ...body };

    // Save settings
    const success = await saveSettings(updatedSettings);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to save settings' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      message: 'Settings saved successfully',
      settings: updatedSettings 
    });
  } catch (error) {
    console.error('Settings POST error:', error);
    return NextResponse.json(
      { error: 'Failed to save settings' },
      { status: 500 }
    );
  }
}
