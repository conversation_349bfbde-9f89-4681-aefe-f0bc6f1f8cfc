'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, CheckCircle, XCircle, LogOut, RefreshCw } from 'lucide-react';
import { USER_STATUS } from '@/lib/db/schema';

export default function PendingApproval() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userStatus, setUserStatus] = useState<'pending' | 'approved' | 'rejected' | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // If user is not logged in, redirect to signin
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    // If user is authenticated, check their status
    if (session?.user && status === 'authenticated') {
      checkUserStatus();
    }
  }, [session, status, router]);

  const checkUserStatus = async () => {
    try {
      setLoading(true);
      
      // Check status from session first
      if (session?.user?.status === USER_STATUS.APPROVED) {
        setUserStatus('approved');
        router.push('/dashboard');
        return;
      } else if (session?.user?.status === USER_STATUS.PENDING) {
        setUserStatus('pending');
      } else if (session?.user?.status === USER_STATUS.REJECTED) {
        setUserStatus('rejected');
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              {userStatus === 'pending' && <Clock className="w-8 h-8 text-white" />}
              {userStatus === 'approved' && <CheckCircle className="w-8 h-8 text-white" />}
              {userStatus === 'rejected' && <XCircle className="w-8 h-8 text-white" />}
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {userStatus === 'pending' && 'Account Pending Approval'}
              {userStatus === 'approved' && 'Account Approved!'}
              {userStatus === 'rejected' && 'Account Rejected'}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="text-center space-y-6">
            {userStatus === 'pending' && (
              <>
                <div className="space-y-4">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-2 text-yellow-800">
                      <Clock className="w-5 h-5" />
                      <span className="font-medium">Awaiting Review</span>
                    </div>
                    <p className="text-yellow-700 text-sm mt-2">
                      Your account is currently being reviewed by our administrators.
                    </p>
                  </div>

                  <div className="text-gray-600 space-y-2">
                    <p>
                      Thank you for registering with <strong>Wikify Blog</strong>!
                    </p>
                    <p className="text-sm">
                      We manually review all new accounts to ensure the quality and security of our community.
                      This process typically takes 24-48 hours.
                    </p>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button 
                    onClick={handleRefresh}
                    variant="outline"
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </Button>
                  <Button 
                    onClick={handleSignOut}
                    variant="outline"
                    className="flex-1"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </>
            )}

            {userStatus === 'approved' && (
              <>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-2 text-green-800">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">Welcome to Wikify Blog!</span>
                  </div>
                  <p className="text-green-700 text-sm mt-2">
                    Your account has been approved. You can now access all features.
                  </p>
                </div>
                
                <Button 
                  onClick={() => router.push('/dashboard')}
                  className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                >
                  Go to Dashboard
                </Button>
              </>
            )}

            {userStatus === 'rejected' && (
              <>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-2 text-red-800">
                    <XCircle className="w-5 h-5" />
                    <span className="font-medium">Account Rejected</span>
                  </div>
                  <p className="text-red-700 text-sm mt-2">
                    Unfortunately, your account application has been rejected. Please contact support for more information.
                  </p>
                </div>
                
                <Button 
                  onClick={handleSignOut}
                  className="w-full bg-gradient-to-r from-red-600 to-gray-600 hover:from-red-700 hover:to-gray-700"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
