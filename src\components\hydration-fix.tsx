'use client';

import { useEffect } from 'react';

/**
 * HydrationFix component handles common hydration issues caused by browser extensions
 * This should be included in your root layout to prevent hydration mismatches
 */
export default function HydrationFix() {
  useEffect(() => {
    // Remove browser extension attributes that cause hydration mismatches
    const body = document.body;
    
    // Common browser extension attributes that cause hydration issues
    const extensionAttributes = [
      'cz-shortcut-listen', // ColorZilla
      'data-new-gr-c-s-check-loaded', // Grammarly
      'data-gr-ext-installed', // Grammarly
      'spellcheck', // Various spell checkers
    ];

    // Clean up extension attributes on mount
    extensionAttributes.forEach(attr => {
      if (body.hasAttribute(attr)) {
        body.removeAttribute(attr);
      }
    });

    // Set up a mutation observer to handle dynamically added attributes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.target === body) {
          const attributeName = mutation.attributeName;
          if (attributeName && extensionAttributes.includes(attributeName)) {
            body.removeAttribute(attributeName);
          }
        }
      });
    });

    // Start observing
    observer.observe(body, {
      attributes: true,
      attributeFilter: extensionAttributes,
    });

    // Cleanup observer on unmount
    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
}
