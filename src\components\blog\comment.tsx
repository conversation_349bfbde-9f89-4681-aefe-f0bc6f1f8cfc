'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';

interface CommentProps {
  comment: {
    id: number;
    content: string;
    date: string;
    approved: boolean;
    parentId?: number | null;
    author: {
      id?: number;
      username?: string;
      displayName?: string;
      email?: string;
      name?: string;
    };
  };
  onReply?: (commentId: number, content: string) => void;
  level?: number;
  maxLevel?: number;
}

const Comment: React.FC<CommentProps> = ({
  comment,
  onReply,
  level = 0,
  maxLevel = 3,
}) => {
  const { data: session } = useSession();
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [replyAuthorName, setReplyAuthorName] = useState('');
  const [replyAuthorEmail, setReplyAuthorEmail] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!replyContent.trim()) return;
    
    if (!session && (!replyAuthorName.trim() || !replyAuthorEmail.trim())) {
      return;
    }

    setSubmitting(true);

    try {
      if (onReply) {
        await onReply(comment.id, replyContent);
        setReplyContent('');
        setReplyAuthorName('');
        setReplyAuthorEmail('');
        setShowReplyForm(false);
      }
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const authorName = comment.author.displayName || comment.author.username || comment.author.name || 'Anonymous';

  return (
    <div className={`${level > 0 ? 'ml-8 mt-4' : 'mt-6'} ${level > 0 ? 'border-l-2 border-gray-200 pl-4' : ''}`}>
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        {/* Comment Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {authorName.charAt(0).toUpperCase()}
            </div>
            <div>
              <p className="font-medium text-gray-900">{authorName}</p>
              <p className="text-xs text-gray-500">{formatDate(comment.date)}</p>
            </div>
          </div>
          
          {!comment.approved && (
            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
              Pending Approval
            </span>
          )}
        </div>

        {/* Comment Content */}
        <div className="prose prose-sm max-w-none mb-4">
          <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
        </div>

        {/* Comment Actions */}
        {level < maxLevel && (
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowReplyForm(!showReplyForm)}
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Reply
            </button>
          </div>
        )}

        {/* Reply Form */}
        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="mt-4 space-y-4 bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900">Reply to {authorName}</h4>
            
            {!session && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Your Name"
                  value={replyAuthorName}
                  onChange={(e) => setReplyAuthorName(e.target.value)}
                  required
                  placeholder="Enter your name"
                />
                <Input
                  label="Your Email"
                  type="email"
                  value={replyAuthorEmail}
                  onChange={(e) => setReplyAuthorEmail(e.target.value)}
                  required
                  placeholder="Enter your email"
                />
              </div>
            )}
            
            <Textarea
              label="Your Reply"
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              required
              placeholder="Write your reply..."
              rows={3}
            />
            
            <div className="flex items-center space-x-2">
              <Button
                type="submit"
                size="sm"
                disabled={submitting}
                loading={submitting}
              >
                Post Reply
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowReplyForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Comment;
