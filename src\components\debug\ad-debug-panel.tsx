'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';

/**
 * Ad Debug Panel - For testing ad functionality
 * This component should only be used in development
 */
export default function AdDebugPanel() {
  const { data: session, update } = useSession();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const grantAdsPermission = async () => {
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/user/grant-ads-permission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setMessage('✅ Ads permission granted! Please refresh the page.');
        // Update the session to reflect the new permission
        await update();
      } else {
        setMessage(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testAdScript = () => {
    // Test if ad scripts are executing
    const adContainers = document.querySelectorAll('.ad-container');
    const scriptCount = document.querySelectorAll('.ad-container script').length;
    
    setMessage(`Found ${adContainers.length} ad containers with ${scriptCount} scripts`);
    
    // Log ad container contents for debugging
    adContainers.forEach((container, index) => {
      console.log(`Ad Container ${index + 1}:`, container.innerHTML);
    });
  };

  return (
    <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-yellow-800 mb-2">🐛 Ad Debug Panel</h3>
      <div className="text-sm text-yellow-700 mb-3">
        <p><strong>User:</strong> {session?.user?.username || 'Not logged in'}</p>
        <p><strong>Role:</strong> {session?.user?.role || 'N/A'}</p>
        <p><strong>Can Insert Ads:</strong> {session?.user?.canInsertAds ? '✅ Yes' : '❌ No'}</p>
      </div>
      
      <div className="space-y-2">
        <Button
          onClick={grantAdsPermission}
          disabled={loading}
          size="sm"
          className="w-full"
        >
          {loading ? 'Granting...' : 'Grant Ads Permission'}
        </Button>
        
        <Button
          onClick={testAdScript}
          variant="outline"
          size="sm"
          className="w-full"
        >
          Test Ad Scripts
        </Button>
      </div>
      
      {message && (
        <div className="mt-2 text-xs text-yellow-800 bg-yellow-50 p-2 rounded">
          {message}
        </div>
      )}
    </div>
  );
}
