import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts, users } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

// GET /api/posts/[id]/edit - Get post data for editing (with permission check)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Await params for Next.js 15 compatibility
    const { id } = await params;
    const postId = parseInt(id);

    if (isNaN(postId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    const { post, author } = result[0];

    // Check if user has permission to edit this post
    const isOwner = post.post_author === parseInt(session.user.id);
    const isAdmin = session.user.role === 'ADMIN';
    const isEditor = session.user.role === 'EDITOR';

    if (!isOwner && !isAdmin && !isEditor) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'You do not have permission to edit this post. Only the post author or administrators can edit posts.' 
        },
        { status: 403 }
      );
    }

    // Get post categories
    const postCategories = await db.execute(sql`
      SELECT t.term_id, t.name, t.slug
      FROM wikify1h_terms t
      INNER JOIN wikify1h_term_taxonomy tt ON t.term_id = tt.term_id
      INNER JOIN wikify1h_term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
      WHERE tr.object_id = ${post.ID} AND tt.taxonomy = 'category'
    `);

    // Extract the actual data from the result array (Drizzle returns [data, metadata])
    const categoryData = postCategories[0]; // First element is the data array
    const categories = Array.isArray(categoryData) ? categoryData.map((cat: any) => cat.term_id) : [];

    // Get ad codes from postmeta
    const adMeta = await db.execute(sql`
      SELECT meta_key, meta_value
      FROM wikify1h_postmeta
      WHERE post_id = ${post.ID} AND meta_key IN ('_before_content_ads', '_after_content_ads')
    `);

    let beforeContentAds = '';
    let afterContentAds = '';

    // Extract the actual data from the result array (Drizzle returns [data, metadata])
    const adMetaData = adMeta[0]; // First element is the data array
    if (Array.isArray(adMetaData)) {
      adMetaData.forEach((meta: any) => {
        if (meta.meta_key === '_before_content_ads') {
          beforeContentAds = meta.meta_value || '';
        } else if (meta.meta_key === '_after_content_ads') {
          afterContentAds = meta.meta_value || '';
        }
      });
    }

    // Get featured image from postmeta
    const featuredImageMeta = await db.execute(sql`
      SELECT meta_value
      FROM wikify1h_postmeta
      WHERE post_id = ${post.ID} AND meta_key = '_thumbnail_url'
      LIMIT 1
    `);

    let featuredImage = '';
    // Extract the actual data from the result array (Drizzle returns [data, metadata])
    const imageMetaData = featuredImageMeta[0]; // First element is the data array
    if (Array.isArray(imageMetaData) && imageMetaData.length > 0) {
      featuredImage = imageMetaData[0].meta_value || '';
    }

    const postData = {
      id: post.ID,
      title: post.post_title,
      content: post.post_content,
      excerpt: post.post_excerpt,
      status: post.post_status,
      slug: post.post_name,
      date: post.post_date,
      modified: post.post_modified,
      categories,
      featuredImage,
      beforeContentAds,
      afterContentAds,
      author: author ? {
        id: author.ID,
        username: author.user_login,
        displayName: author.display_name,
        email: author.user_email
      } : null
    };



    return NextResponse.json({
      success: true,
      data: postData
    });

  } catch (error) {
    console.error('Error fetching post for editing:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch post data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
