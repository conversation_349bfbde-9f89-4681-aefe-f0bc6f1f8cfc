import Link from 'next/link';

interface CategoryCardProps {
  category: {
    id: number;
    name: string;
    slug: string;
    description: string;
    count: number;
  };
  size?: 'xs' | 'small' | 'medium' | 'large';
  showDescription?: boolean;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  size = 'medium',
  showDescription = false,
}) => {
  // Get emoji for category
  const getCategoryEmoji = (name: string) => {
    const emojiMap: { [key: string]: string } = {
      'Technology': '💻',
      'Health': '🏥',
      'Travel': '✈️',
      'Food': '🍽️',
      'Lifestyle': '🌟',
      'Sports': '⚽',
      'Music': '🎵',
      'Art': '🎨',
      'Science': '🔬',
      'Business': '💼',
      'Education': '📚',
      'Fashion': '👗',
      'Gaming': '🎮',
      'Photography': '📸',
      'Fitness': '💪',
      'Cooking': '👨‍🍳',
      'Nature': '🌿',
      'Movies': '🎬',
      'Books': '📖',
      'News': '📰',
    };
    
    return emojiMap[name] || '📁';
  };

  // Get gradient colors for category
  const getCategoryGradient = (name: string) => {
    const gradientMap: { [key: string]: string } = {
      'Technology': 'from-blue-50 to-indigo-100 border-blue-200 hover:from-blue-100 hover:to-indigo-200 hover:border-blue-300',
      'Health': 'from-green-50 to-emerald-100 border-green-200 hover:from-green-100 hover:to-emerald-200 hover:border-green-300',
      'Travel': 'from-purple-50 to-violet-100 border-purple-200 hover:from-purple-100 hover:to-violet-200 hover:border-purple-300',
      'Food': 'from-orange-50 to-amber-100 border-orange-200 hover:from-orange-100 hover:to-amber-200 hover:border-orange-300',
      'Lifestyle': 'from-pink-50 to-rose-100 border-pink-200 hover:from-pink-100 hover:to-rose-200 hover:border-pink-300',
      'Sports': 'from-red-50 to-red-100 border-red-200 hover:from-red-100 hover:to-red-200 hover:border-red-300',
      'Music': 'from-indigo-50 to-purple-100 border-indigo-200 hover:from-indigo-100 hover:to-purple-200 hover:border-indigo-300',
      'Art': 'from-yellow-50 to-orange-100 border-yellow-200 hover:from-yellow-100 hover:to-orange-200 hover:border-yellow-300',
    };
    
    return gradientMap[name] || 'from-gray-50 to-gray-100 border-gray-200 hover:from-gray-100 hover:to-gray-200 hover:border-gray-300';
  };

  const sizeClasses = {
    xs: 'px-4 py-3 text-center whitespace-nowrap min-w-[120px]',
    small: 'px-5 py-4 text-center min-w-[140px]',
    medium: 'px-6 py-5 text-center min-w-[160px]',
    large: 'px-8 py-6 text-center min-w-[180px]',
  };

  const emojiSizes = {
    xs: 'text-lg',
    small: 'text-xl',
    medium: 'text-2xl',
    large: 'text-3xl',
  };

  const titleSizes = {
    xs: 'text-xs',
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
  };



  return (
    <Link
      href={`/search?category=${category.slug}`}
      className="group block"
    >
      <div className={`
        bg-gradient-to-br ${getCategoryGradient(category.name)}
        rounded-2xl ${sizeClasses[size]}
        hover:shadow-lg hover:scale-105
        transition-all duration-300
        border-2 backdrop-blur-sm
        relative overflow-hidden
        group-hover:shadow-xl
      `}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-white rounded-full -translate-y-8 translate-x-8"></div>
          <div className="absolute bottom-0 left-0 w-12 h-12 bg-white rounded-full translate-y-6 -translate-x-6"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col items-center space-y-1">
          <div className={`${emojiSizes[size]} mb-1 group-hover:scale-110 transition-transform duration-300`}>
            {getCategoryEmoji(category.name)}
          </div>
          <h3 className={`font-semibold text-gray-800 ${titleSizes[size]} group-hover:text-gray-900 transition-colors leading-tight`}>
            {category.name}
          </h3>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;
