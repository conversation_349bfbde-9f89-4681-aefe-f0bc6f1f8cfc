{"name": "wikify", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toast": "^1.2.15", "@tiptap/extension-code-block-lowlight": "^3.2.0", "@tiptap/extension-color": "^3.2.0", "@tiptap/extension-font-family": "^3.2.0", "@tiptap/extension-heading": "^3.2.0", "@tiptap/extension-highlight": "^3.2.0", "@tiptap/extension-image": "^3.2.0", "@tiptap/extension-link": "^3.2.0", "@tiptap/extension-subscript": "^3.2.0", "@tiptap/extension-superscript": "^3.2.0", "@tiptap/extension-table": "^3.2.0", "@tiptap/extension-table-cell": "^3.2.0", "@tiptap/extension-table-header": "^3.2.0", "@tiptap/extension-table-row": "^3.2.0", "@tiptap/extension-text-align": "^3.2.0", "@tiptap/extension-text-style": "^3.2.0", "@tiptap/extension-underline": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^7.0.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "lowlight": "^3.3.0", "lucide-react": "^0.539.0", "mysql2": "^3.14.3", "next": "15.4.6", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "puppeteer": "^24.17.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}