import Link from 'next/link';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-800 border-t border-gray-200">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="mb-4">
              <img
                src="https://i.ibb.co.com/Z1VVmJCT/Untitled-design-5.gif"
                alt="Wikify"
                className="h-20 w-auto"
              />
            </div>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Learn how to do anything with Wikify, the world's most popular how-to website. Easy, well-researched, and trustworthy instructions for everything you want to know.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm font-bold text-gray-800 uppercase tracking-wider mb-6">
              Quick Links
            </h4>
            <ul className="space-y-3">

              <li>
                <Link href="/categories" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200 flex items-center group">
                  <svg className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Categories
                </Link>
              </li>
              <li>
                <Link href="/author" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200 flex items-center group">
                  <svg className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Authors
                </Link>
              </li>
              <li>
                <Link href="/search" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200 flex items-center group">
                  <svg className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Search
                </Link>
              </li>
            </ul>
          </div>


        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-300">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-500 text-sm">
              © {currentYear} Wikify Blog. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-gray-500 hover:text-indigo-600 text-sm transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-500 hover:text-indigo-600 text-sm transition-colors duration-200">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-gray-500 hover:text-indigo-600 text-sm transition-colors duration-200">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
