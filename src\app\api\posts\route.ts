import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts, users, postmeta, term_relationships, term_taxonomy, terms } from '@/lib/db/schema';
import { eq, desc, and, like, or, sql, inArray } from 'drizzle-orm';
import { extractFeaturedImage, processWordPressContent, generateExcerpt, getFeaturedImage } from '@/lib/utils/content-processor';
import { generateUniquePostSlug } from '@/lib/utils/slug-server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const author = searchParams.get('author');
    const status = searchParams.get('status') || 'publish';

    const offset = (page - 1) * limit;

    // Handle multiple statuses (comma-separated)
    const statusArray = status.split(',').map(s => s.trim());
    console.log('Fetching posts with status:', statusArray);

    let whereConditions = [eq(posts.post_type, 'post')];

    // Add status filter - exclude trash
    if (statusArray.includes('publish') && statusArray.includes('draft')) {
      // For dashboard: show publish and draft, exclude trash
      whereConditions.push(or(
        eq(posts.post_status, 'publish'),
        eq(posts.post_status, 'draft')
      ));
    } else {
      // Single status
      whereConditions.push(eq(posts.post_status, statusArray[0]));
    }

    // Add search filter
    if (search) {
      whereConditions.push(or(
        like(posts.post_title, `%${search}%`),
        like(posts.post_content, `%${search}%`)
      ));
    }

    // Add author filter
    if (author) {
      whereConditions.push(eq(posts.post_author, parseInt(author)));
    }

    let query = db
      .selectDistinct({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(...whereConditions));

    const result = await query
      .orderBy(desc(posts.post_date))
      .limit(limit)
      .offset(offset);

    console.log(`Found ${result.length} posts for author ${author} with status ${statusArray.join(',')}`);
    console.log('Post statuses:', result.map(r => ({ id: r.post.ID, status: r.post.post_status })));

    // Filter out trash posts as an extra safety measure
    const filteredResult = result.filter(r => r.post.post_status !== 'trash');
    console.log(`After filtering trash: ${filteredResult.length} posts`);

    // Format response with featured images
    const formattedPosts = await Promise.all(
      filteredResult.map(async ({ post, author }) => ({
        id: post.ID,
        title: post.post_title,
        slug: post.post_name,
        excerpt: post.post_excerpt || generateExcerpt(post.post_content),
        content: processWordPressContent(post.post_content),
        status: post.post_status,
        date: post.post_date,
        modified: post.post_modified,
        featured_image: await getFeaturedImage(post.ID, post.post_content),
        author: author ? {
          id: author.ID,
          username: author.user_login,
          displayName: author.display_name,
          email: author.user_email
        } : null,
        commentCount: post.comment_count
      }))
    );

    // Final deduplication by ID to ensure no duplicates
    const uniquePosts = formattedPosts.filter((post, index, self) =>
      index === self.findIndex(p => p.id === post.id)
    );

    // Get total count for pagination
    const countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(and(...whereConditions));

    const [{ count: totalItems }] = await countQuery;
    const totalPages = Math.ceil(totalItems / limit);

    return NextResponse.json({
      success: true,
      data: uniquePosts,
      pagination: {
        page,
        limit,
        totalItems,
        totalPages,
        hasMore: page < totalPages,
        hasPrevious: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch posts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, content, excerpt, status = 'draft', featuredImage, categories = [], beforeContentAds = '', afterContentAds = '' } = body;

    if (!title || !content) {
      return NextResponse.json(
        { success: false, error: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Generate unique slug from title
    const slug = await generateUniquePostSlug(title);

    // Create current datetime in MySQL format
    const now = new Date();
    const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');

    // Use sql template to avoid datetime conversion issues
    const result = await db.execute(sql`
      INSERT INTO wikify1h_posts (
        post_title, post_content, post_excerpt, post_name, post_status,
        post_author, post_type, post_date, post_date_gmt, post_modified,
        post_modified_gmt, guid, comment_status, ping_status, post_password,
        to_ping, pinged, post_content_filtered, post_parent, menu_order,
        post_mime_type, comment_count
      ) VALUES (
        ${title}, ${content}, ${excerpt || ''}, ${slug}, ${status},
        ${parseInt(session.user.id)}, ${'post'}, ${mysqlDateTime}, ${mysqlDateTime},
        ${mysqlDateTime}, ${mysqlDateTime},
        ${`${process.env.APP_URL || 'http://localhost:3000'}/?p=${Date.now()}`},
        ${'open'}, ${'open'}, ${''}, ${''}, ${''}, ${''}, ${0}, ${0}, ${''}, ${0}
      )
    `);

    // Get the inserted ID
    const insertId = result.insertId || result[0]?.insertId;

    // If featured image is provided, save it as post meta
    if (featuredImage && insertId) {
      try {
        const { postmeta } = await import('@/lib/db/schema');
        await db.execute(sql`
          INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
          VALUES (${insertId}, '_thumbnail_url', ${featuredImage})
        `);
      } catch (metaError) {
        console.error('Error saving featured image meta:', metaError);
        // Don't fail the whole request if meta save fails
      }
    }

    // Save ad codes as post meta if provided
    if (insertId) {
      try {
        const { postmeta } = await import('@/lib/db/schema');

        // Save before content ads
        if (beforeContentAds) {
          await db.execute(sql`
            INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
            VALUES (${insertId}, '_before_content_ads', ${beforeContentAds})
          `);
        }

        // Save after content ads
        if (afterContentAds) {
          await db.execute(sql`
            INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
            VALUES (${insertId}, '_after_content_ads', ${afterContentAds})
          `);
        }
      } catch (adMetaError) {
        console.error('Error saving ad meta:', adMetaError);
        // Don't fail the whole request if ad meta save fails
      }
    }

    // Save categories if provided
    if (categories && categories.length > 0 && insertId) {
      try {
        // Get term_taxonomy_ids for the selected categories
        const categoryTaxonomies = await db
          .select({ term_taxonomy_id: term_taxonomy.term_taxonomy_id })
          .from(term_taxonomy)
          .where(and(
            eq(term_taxonomy.taxonomy, 'category'),
            inArray(term_taxonomy.term_id, categories)
          ));

        // Insert relationships
        for (const taxonomy of categoryTaxonomies) {
          await db.execute(sql`
            INSERT INTO wikify1h_term_relationships (object_id, term_taxonomy_id, term_order)
            VALUES (${insertId}, ${taxonomy.term_taxonomy_id}, 0)
          `);
        }
      } catch (categoryError) {
        console.error('Error saving categories:', categoryError);
        // Don't fail the whole request if category save fails
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        id: insertId,
        title,
        slug,
        excerpt,
        content,
        status,
        featuredImage,
        categories
      }
    });

  } catch (error) {
    console.error('Error creating post:', error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
