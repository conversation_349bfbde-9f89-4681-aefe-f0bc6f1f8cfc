import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, posts, usermeta } from '@/lib/db/schema';
import { eq, count, desc } from 'drizzle-orm';

// GET /api/authors/[slug] - Get public author profile by slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Author slug is required' },
        { status: 400 }
      );
    }

    // Get author by user_nicename (slug)
    const authorResult = await db
      .select({
        user: users,
        bio: usermeta
      })
      .from(users)
      .leftJoin(usermeta, eq(users.ID, usermeta.user_id))
      .where(eq(users.user_nicename, slug))
      .limit(1);

    if (!authorResult.length) {
      return NextResponse.json(
        { success: false, error: 'Author not found' },
        { status: 404 }
      );
    }

    const { user } = authorResult[0];

    // Get author's bio from usermeta
    const bioResult = await db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, user.ID))
      .where(eq(usermeta.meta_key, 'description'));

    const bio = bioResult.length > 0 ? bioResult[0].meta_value : '';

    // Get author's role from capabilities
    const capabilitiesResult = await db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, user.ID))
      .where(eq(usermeta.meta_key, 'wikify1h_capabilities'));

    let userRole = 'AUTHOR';
    if (capabilitiesResult.length > 0 && capabilitiesResult[0].meta_value) {
      const caps = capabilitiesResult[0].meta_value;
      if (caps.includes('administrator')) userRole = 'ADMIN';
      else if (caps.includes('editor')) userRole = 'EDITOR';
      else if (caps.includes('author')) userRole = 'AUTHOR';
    }

    // Get author statistics
    const [postCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(eq(posts.post_author, user.ID))
      .where(eq(posts.post_status, 'publish'))
      .where(eq(posts.post_type, 'post'));

    // Get author's recent posts
    const recentPosts = await db
      .select({
        id: posts.ID,
        title: posts.post_title,
        slug: posts.post_name,
        excerpt: posts.post_excerpt,
        date: posts.post_date,
        commentCount: posts.comment_count
      })
      .from(posts)
      .where(eq(posts.post_author, user.ID))
      .where(eq(posts.post_status, 'publish'))
      .where(eq(posts.post_type, 'post'))
      .orderBy(desc(posts.post_date))
      .limit(5);

    return NextResponse.json({
      success: true,
      data: {
        id: user.ID,
        username: user.user_login,
        displayName: user.display_name || user.user_login,
        nicename: user.user_nicename,
        email: user.user_email,
        url: user.user_url,
        bio: bio || '',
        registered: user.user_registered,
        role: userRole,
        stats: {
          posts: postCount.count
        },
        recentPosts: recentPosts.map(post => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt || '',
          date: post.date,
          commentCount: post.commentCount
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching author profile:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch author profile',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
