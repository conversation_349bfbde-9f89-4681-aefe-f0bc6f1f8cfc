import { db } from '@/lib/db';
import { posts } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface PostPermissionCheck {
  canEdit: boolean;
  canDelete: boolean;
  isOwner: boolean;
  isAdmin: boolean;
  isEditor: boolean;
}

/**
 * Check if a user has permission to edit/delete a specific post
 */
export async function checkPostPermissions(
  postId: number,
  userId: number,
  userRole: string
): Promise<PostPermissionCheck> {
  try {
    // Get the post to check ownership
    const post = await db
      .select({ post_author: posts.post_author })
      .from(posts)
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!post.length) {
      return {
        canEdit: false,
        canDelete: false,
        isOwner: false,
        isAdmin: false,
        isEditor: false,
      };
    }

    const isOwner = post[0].post_author === userId;
    const isAdmin = userRole === 'ADMIN';
    const isEditor = userRole === 'EDITOR';

    return {
      canEdit: isOwner || isAdmin || isEditor,
      canDelete: isOwner || isAdmin,
      isOwner,
      isAdmin,
      isEditor,
    };
  } catch (error) {
    console.error('Error checking post permissions:', error);
    return {
      canEdit: false,
      canDelete: false,
      isOwner: false,
      isAdmin: false,
      isEditor: false,
    };
  }
}

/**
 * Check if a user can edit a post (simplified version)
 */
export async function canUserEditPost(
  postId: number,
  userId: number,
  userRole: string
): Promise<boolean> {
  const permissions = await checkPostPermissions(postId, userId, userRole);
  return permissions.canEdit;
}

/**
 * Check if a user can delete a post (simplified version)
 */
export async function canUserDeletePost(
  postId: number,
  userId: number,
  userRole: string
): Promise<boolean> {
  const permissions = await checkPostPermissions(postId, userId, userRole);
  return permissions.canDelete;
}

/**
 * Middleware helper to check post edit permissions
 */
export function hasPostEditPermission(
  postAuthorId: number,
  currentUserId: number,
  currentUserRole: string
): boolean {
  const isOwner = postAuthorId === currentUserId;
  const isAdmin = currentUserRole === 'ADMIN';
  const isEditor = currentUserRole === 'EDITOR';
  
  return isOwner || isAdmin || isEditor;
}

/**
 * Middleware helper to check post delete permissions
 */
export function hasPostDeletePermission(
  postAuthorId: number,
  currentUserId: number,
  currentUserRole: string
): boolean {
  const isOwner = postAuthorId === currentUserId;
  const isAdmin = currentUserRole === 'ADMIN';
  
  return isOwner || isAdmin;
}
