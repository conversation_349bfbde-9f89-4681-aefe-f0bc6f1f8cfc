'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';

interface FilterOption {
  id: string;
  name: string;
  slug: string;
  count?: number;
}

interface PostFiltersProps {
  categories?: FilterOption[];
  tags?: FilterOption[];
  authors?: FilterOption[];
  onFilterChange?: (filters: any) => void;
  className?: string;
}

const PostFilters: React.FC<PostFiltersProps> = ({
  categories = [],
  tags = [],
  authors = [],
  onFilterChange,
  className = '',
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [activeFilters, setActiveFilters] = useState({
    category: searchParams.get('category') || '',
    tag: searchParams.get('tag') || '',
    author: searchParams.get('author') || '',
  });

  const updateFilters = (type: string, value: string) => {
    const newFilters = {
      ...activeFilters,
      [type]: activeFilters[type as keyof typeof activeFilters] === value ? '' : value,
    };
    
    setActiveFilters(newFilters);
    
    if (onFilterChange) {
      onFilterChange(newFilters);
    } else {
      // Update URL
      const params = new URLSearchParams(searchParams);
      
      Object.entries(newFilters).forEach(([key, val]) => {
        if (val) {
          params.set(key, val);
        } else {
          params.delete(key);
        }
      });
      
      // Preserve search query
      const query = searchParams.get('q');
      if (query) {
        params.set('q', query);
      }
      
      router.push(`/search?${params.toString()}`);
    }
  };

  const clearAllFilters = () => {
    const newFilters = { category: '', tag: '', author: '' };
    setActiveFilters(newFilters);
    
    if (onFilterChange) {
      onFilterChange(newFilters);
    } else {
      const params = new URLSearchParams();
      const query = searchParams.get('q');
      if (query) {
        params.set('q', query);
      }
      router.push(`/search?${params.toString()}`);
    }
  };

  const hasActiveFilters = Object.values(activeFilters).some(filter => filter !== '');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Clear Filters */}
      {hasActiveFilters && (
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear All
          </Button>
        </div>
      )}

      {/* Categories */}
      {categories.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Categories</h4>
          <div className="space-y-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => updateFilters('category', category.slug)}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                  activeFilters.category === category.slug
                    ? 'bg-blue-100 text-blue-800 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <div className="flex justify-between items-center">
                  <span>{category.name}</span>
                  {category.count !== undefined && (
                    <span className="text-xs text-gray-500">({category.count})</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Tags */}
      {tags.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Tags</h4>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <button
                key={tag.id}
                onClick={() => updateFilters('tag', tag.slug)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  activeFilters.tag === tag.slug
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {tag.name}
                {tag.count !== undefined && (
                  <span className="ml-1 text-gray-500">({tag.count})</span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Authors */}
      {authors.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Authors</h4>
          <div className="space-y-2">
            {authors.map((author) => (
              <button
                key={author.id}
                onClick={() => updateFilters('author', author.id)}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                  activeFilters.author === author.id
                    ? 'bg-blue-100 text-blue-800 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <div className="flex justify-between items-center">
                  <span>{author.name}</span>
                  {author.count !== undefined && (
                    <span className="text-xs text-gray-500">({author.count})</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PostFilters;
