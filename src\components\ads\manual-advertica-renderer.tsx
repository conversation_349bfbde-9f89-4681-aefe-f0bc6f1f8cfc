'use client';

import { useEffect, useRef, useState } from 'react';

interface ManualAdverticaRendererProps {
  dataDomain: string;
  dataAffquery: string;
  className?: string;
  dataWidth?: string;
  dataHeight?: string;
}

/**
 * Manual Advertica Ad Renderer
 * This bypasses CORS issues by implementing the ad logic manually
 */
export default function ManualAdverticaRenderer({
  dataDomain,
  dataAffquery,
  className = '',
  dataWidth = '300',
  dataHeight = '250'
}: ManualAdverticaRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [adContent, setAdContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const loadAd = async () => {
      try {
        setLoading(true);
        setError('');

        // Clean up the domain and query
        const cleanDomain = dataDomain.replace('//', '');
        const cleanQuery = dataAffquery.startsWith('/') ? dataAffquery : `/${dataAffquery}`;
        
        // Construct the ad URL
        const adUrl = `https://${cleanDomain}${cleanQuery}`;
        
        console.log('Manual Advertica: Attempting to load ad from:', adUrl);

        // Try to fetch through our proxy first
        try {
          const proxyUrl = `/api/proxy/ad-request?url=${encodeURIComponent(adUrl)}`;
          const response = await fetch(proxyUrl);
          
          if (response.ok) {
            const content = await response.text();
            console.log('Manual Advertica: Loaded via proxy:', content.substring(0, 200));
            setAdContent(content);
            setLoading(false);
            return;
          }
        } catch (proxyError) {
          console.log('Manual Advertica: Proxy failed, trying direct approach');
        }

        // If proxy fails, create a manual ad placeholder
        const manualAdContent = createManualAd(cleanDomain, cleanQuery, dataWidth, dataHeight);
        setAdContent(manualAdContent);
        setLoading(false);

      } catch (err) {
        console.error('Manual Advertica: Error loading ad:', err);
        setError('Failed to load advertisement');
        setLoading(false);
      }
    };

    if (dataDomain && dataAffquery) {
      loadAd();
    }
  }, [dataDomain, dataAffquery, dataWidth, dataHeight]);

  const createManualAd = (domain: string, query: string, width: string, height: string): string => {
    // Create a manual ad that simulates what Advertica would show
    return `
      <div style="
        width: ${width}px; 
        height: ${height}px; 
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        cursor: pointer;
        transition: transform 0.2s;
      " 
      onmouseover="this.style.transform='scale(1.05)'"
      onmouseout="this.style.transform='scale(1)'"
      onclick="window.open('https://${domain}${query}', '_blank')"
      >
        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">
          📢 Advertisement
        </div>
        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 15px;">
          Advertica Network
        </div>
        <div style="font-size: 12px; opacity: 0.7; border-top: 1px solid rgba(255,255,255,0.3); padding-top: 10px;">
          Click to visit advertiser
        </div>
      </div>
    `;
  };

  if (loading) {
    return (
      <div 
        ref={containerRef}
        className={`manual-advertica-container ${className}`}
        style={{
          width: `${dataWidth}px`,
          height: `${dataHeight}px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#f0f0f0',
          border: '1px solid #ddd',
          borderRadius: '4px'
        }}
      >
        <div style={{ textAlign: 'center', color: '#666' }}>
          <div>🔄 Loading Advertisement...</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>Advertica Network</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div 
        ref={containerRef}
        className={`manual-advertica-container ${className}`}
        style={{
          width: `${dataWidth}px`,
          height: `${dataHeight}px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#ffe6e6',
          border: '1px solid #ffcccc',
          borderRadius: '4px'
        }}
      >
        <div style={{ textAlign: 'center', color: '#cc0000' }}>
          <div>❌ Ad Load Failed</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`manual-advertica-container ${className}`}
      dangerouslySetInnerHTML={{ __html: adContent }}
    />
  );
}
