'use client';

import { useEffect } from 'react';
import Script from 'next/script';

interface AnalyticsProps {
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  hotjarId?: string;
}

export default function Analytics({
  googleAnalyticsId,
  googleTagManagerId,
  facebookPixelId,
  hotjarId
}: AnalyticsProps) {
  
  // Google Analytics 4
  const GA_ID = googleAnalyticsId || process.env.NEXT_PUBLIC_GA_ID;
  
  // Google Tag Manager
  const GTM_ID = googleTagManagerId || process.env.NEXT_PUBLIC_GTM_ID;
  
  // Facebook Pixel
  const FB_PIXEL_ID = facebookPixelId || process.env.NEXT_PUBLIC_FB_PIXEL_ID;
  
  // Hotjar
  const HOTJAR_ID = hotjarId || process.env.NEXT_PUBLIC_HOTJAR_ID;

  return (
    <>
      {/* Google Analytics 4 */}
      {GA_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA_ID}', {
                page_title: document.title,
                page_location: window.location.href,
              });
            `}
          </Script>
        </>
      )}

      {/* Google Tag Manager */}
      {GTM_ID && (
        <>
          <Script id="google-tag-manager" strategy="afterInteractive">
            {`
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${GTM_ID}');
            `}
          </Script>
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            />
          </noscript>
        </>
      )}

      {/* Facebook Pixel */}
      {FB_PIXEL_ID && (
        <Script id="facebook-pixel" strategy="afterInteractive">
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${FB_PIXEL_ID}');
            fbq('track', 'PageView');
          `}
        </Script>
      )}

      {/* Hotjar */}
      {HOTJAR_ID && (
        <Script id="hotjar" strategy="afterInteractive">
          {`
            (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
          `}
        </Script>
      )}

      {/* Schema.org Organization */}
      <Script id="organization-schema" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Wikify",
            "url": "${process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz'}",
            "description": "Your trusted source for comprehensive guides and tutorials",
            "logo": "${process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz'}/logo.png",
            "sameAs": [
              "https://facebook.com/wikify",
              "https://twitter.com/wikify",
              "https://instagram.com/wikify",
              "https://linkedin.com/company/wikify"
            ]
          }
        `}
      </Script>

      {/* Website Schema */}
      <Script id="website-schema" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "Wikify",
            "url": "${process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz'}",
            "description": "How to Guide you can Trust - Your trusted source for comprehensive guides and tutorials",
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": "${process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz'}/search?q={search_term_string}"
              },
              "query-input": "required name=search_term_string"
            }
          }
        `}
      </Script>
    </>
  );
}
