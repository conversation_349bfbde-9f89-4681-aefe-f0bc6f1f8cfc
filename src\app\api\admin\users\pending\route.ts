import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, USER_STATUS } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET /api/admin/users/pending - Get pending users
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get pending users
    const pendingUsers = await db
      .select({
        id: users.ID,
        username: users.user_login,
        email: users.user_email,
        displayName: users.display_name,
        registered: users.user_registered,
        status: users.user_status,
        mobile: users.user_mobile
      })
      .from(users)
      .where(eq(users.user_status, USER_STATUS.PENDING))
      .orderBy(desc(users.user_registered))
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await db
      .select({ count: users.ID })
      .from(users)
      .where(eq(users.user_status, USER_STATUS.PENDING));

    const total = totalResult.length;

    return NextResponse.json({
      success: true,
      data: pendingUsers.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayName,
        registered: user.registered,
        status: 'pending',
        mobile: user.mobile
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching pending users:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch pending users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
