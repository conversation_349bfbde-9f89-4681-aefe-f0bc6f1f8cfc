'use client';

import { useEffect, useRef, useState } from 'react';
import EditableCodeBlock from './editable-code-block';
import Toast from './toast';

interface PostContentRendererProps {
  content: string;
  isEditable?: boolean;
  onContentUpdate?: (newContent: string) => void;
  className?: string;
  postId?: number;
}

const PostContentRenderer: React.FC<PostContentRendererProps> = ({
  content,
  isEditable = false,
  onContentUpdate,
  className = '',
  postId
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [processedContent, setProcessedContent] = useState(content);
  const [saving, setSaving] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  const saveContentToServer = async (newContent: string) => {
    if (!postId || saving) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/posts/${postId}/content`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newContent }),
      });

      if (!response.ok) {
        throw new Error('Failed to save content');
      }

      setToast({ message: 'Code saved successfully!', type: 'success' });
    } catch (error) {
      console.error('Error saving content:', error);
      setToast({ message: 'Failed to save code!', type: 'error' });
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    if (!contentRef.current || !isEditable) return;

    const container = contentRef.current;
    
    // Find all code blocks
    const codeBlocks = container.querySelectorAll('pre.bg-gray-900');
    
    codeBlocks.forEach((preElement, index) => {
      const codeElement = preElement.querySelector('code');
      if (!codeElement) return;

      const code = codeElement.textContent || '';
      const classNames = codeElement.className || '';
      const languageMatch = classNames.match(/language-(\w+)/);
      const language = languageMatch ? languageMatch[1] : 'javascript';

      // Create a wrapper div
      const wrapper = document.createElement('div');
      wrapper.className = 'editable-code-wrapper';
      
      // Insert wrapper before the pre element
      preElement.parentNode?.insertBefore(wrapper, preElement);
      
      // Hide the original pre element
      preElement.style.display = 'none';

      // Create and mount the React component
      import('react-dom/client').then(({ createRoot }) => {
        const root = createRoot(wrapper);
        root.render(
          <EditableCodeBlock
            code={code}
            language={language}
            isEditable={true}
            onUpdate={async (newCode, newLanguage) => {
              // Update the original code element
              codeElement.textContent = newCode;
              codeElement.className = `language-${newLanguage}`;

              // Update the processed content
              const updatedContent = container.innerHTML;
              setProcessedContent(updatedContent);

              // Save to server if postId is provided
              if (postId) {
                await saveContentToServer(updatedContent);
              }

              if (onContentUpdate) {
                onContentUpdate(updatedContent);
              }
            }}
          />
        );
      });
    });

    // Cleanup function
    return () => {
      const wrappers = container.querySelectorAll('.editable-code-wrapper');
      wrappers.forEach(wrapper => {
        wrapper.remove();
      });
      
      // Show original pre elements
      const hiddenPres = container.querySelectorAll('pre[style*="display: none"]');
      hiddenPres.forEach(pre => {
        (pre as HTMLElement).style.display = '';
      });
    };
  }, [isEditable, onContentUpdate]);

  return (
    <div className="relative">
      {saving && (
        <div className="absolute top-0 right-0 bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm font-medium z-10">
          Saving...
        </div>
      )}
      <div
        ref={contentRef}
        className={className}
        dangerouslySetInnerHTML={{ __html: processedContent }}
      />

      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default PostContentRenderer;
