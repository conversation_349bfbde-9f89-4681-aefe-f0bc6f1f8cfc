import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as schema from './schema';

// Create the connection pool for better performance
const poolConnection = mysql.createPool({
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '3306'),
  user: process.env.DATABASE_USERNAME || 'root',
  password: process.env.DATABASE_PASSWORD || '',
  database: process.env.DATABASE_NAME || 'unlifyc2_wikify325',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// Create the drizzle instance
export const db = drizzle(poolConnection, { schema, mode: 'default' });

// Export schema for use in other files
export * from './schema';

// Helper function to test database connection
export async function testConnection() {
  try {
    const connection = await poolConnection.getConnection();
    console.log('Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}
