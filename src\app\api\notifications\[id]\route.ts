import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { notifications } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// PATCH /api/notifications/[id] - Mark notification as read
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const notificationId = parseInt(id);
    const userId = parseInt(session.user.id);

    if (isNaN(notificationId) || isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid notification ID or user session' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { is_read } = body;

    if (typeof is_read !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'is_read must be a boolean value' },
        { status: 400 }
      );
    }

    // Check if notification exists and belongs to user
    const existingNotification = await db
      .select()
      .from(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.user_id, userId)
      ))
      .limit(1);

    if (!existingNotification.length) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Update notification
    await db
      .update(notifications)
      .set({
        is_read,
        read_at: is_read ? new Date() : null
      })
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.user_id, userId)
      ));

    return NextResponse.json({
      success: true,
      data: {
        id: notificationId,
        is_read,
        message: `Notification marked as ${is_read ? 'read' : 'unread'}`
      }
    });

  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update notification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - Delete notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const notificationId = parseInt(id);
    const userId = parseInt(session.user.id);

    if (isNaN(notificationId) || isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid notification ID or user session' },
        { status: 400 }
      );
    }

    // Check if notification exists and belongs to user
    const existingNotification = await db
      .select()
      .from(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.user_id, userId)
      ))
      .limit(1);

    if (!existingNotification.length) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Delete notification
    await db
      .delete(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.user_id, userId)
      ));

    return NextResponse.json({
      success: true,
      data: {
        id: notificationId,
        message: 'Notification deleted successfully'
      }
    });

  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete notification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
