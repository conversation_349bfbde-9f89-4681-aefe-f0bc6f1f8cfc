import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, USER_STATUS } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/user/status - Get current user's approval status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user status from database
    const user = await db
      .select({
        user_status: users.user_status,
        user_login: users.user_login,
        user_email: users.user_email,
        display_name: users.display_name
      })
      .from(users)
      .where(eq(users.ID, parseInt(session.user.id)))
      .limit(1);

    if (!user.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const userStatus = user[0].user_status;
    let status: 'pending' | 'approved' | 'rejected';

    switch (userStatus) {
      case USER_STATUS.PENDING:
        status = 'pending';
        break;
      case USER_STATUS.APPROVED:
        status = 'approved';
        break;
      case USER_STATUS.REJECTED:
        status = 'rejected';
        break;
      default:
        status = 'pending';
    }

    return NextResponse.json({
      success: true,
      status,
      user: {
        username: user[0].user_login,
        email: user[0].user_email,
        displayName: user[0].display_name
      }
    });

  } catch (error) {
    console.error('Error checking user status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check user status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
