import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

async function createNotificationsTable() {
  try {
    console.log('Creating notifications table...');
    
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS notifications (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id BIGINT UNSIGNED NOT NULL,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        post_id BIGINT UNSIGNED NULL,
        post_title VARCHAR(255) NULL,
        admin_id BIGINT UNSIGNED NOT NULL,
        admin_name VARCHAR(100) NOT NULL,
        is_read BOOLEAN NOT NULL DEFAULT FALSE,
        created_at DATETIME NOT NULL,
        read_at DATETIME NULL,
        INDEX user_idx (user_id),
        INDEX type_idx (type),
        INDEX created_idx (created_at),
        INDEX read_idx (is_read)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('Notifications table created successfully!');
  } catch (error) {
    console.error('Error creating notifications table:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  createNotificationsTable()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { createNotificationsTable };
