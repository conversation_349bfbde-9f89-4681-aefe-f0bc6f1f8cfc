import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { canUserInsertAds, setAdsPermission } from '@/lib/utils/ads-permissions';

// GET /api/admin/users/[id]/ads-permission - Get user's ads permission
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Get ads permission
    const canInsertAds = await canUserInsertAds(userId);

    return NextResponse.json({
      success: true,
      data: {
        userId,
        canInsertAds,
        username: existingUser[0].user_login,
        email: existingUser[0].user_email
      }
    });

  } catch (error) {
    console.error('Error getting ads permission:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get ads permission',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users/[id]/ads-permission - Update user's ads permission
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { canInsertAds } = body;

    if (typeof canInsertAds !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'canInsertAds must be a boolean value' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update ads permission
    const success = await setAdsPermission(userId, canInsertAds);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to update ads permission' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        userId,
        canInsertAds,
        username: existingUser[0].user_login,
        message: `Ads permission ${canInsertAds ? 'granted' : 'revoked'} successfully`
      }
    });

  } catch (error) {
    console.error('Error updating ads permission:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update ads permission',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
