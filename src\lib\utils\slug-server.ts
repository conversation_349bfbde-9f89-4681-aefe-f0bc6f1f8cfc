import { db } from '@/lib/db';
import { posts } from '@/lib/db/schema';
import { eq, and, ne } from 'drizzle-orm';
import { generateSlug } from './slug';

/**
 * Check if a slug exists for posts (excluding trashed posts)
 * This function requires database access and should only be used server-side
 */
export async function checkPostSlugExists(slug: string, excludePostId?: number): Promise<boolean> {
  try {
    const conditions = [
      eq(posts.post_name, slug),
      eq(posts.post_type, 'post'),
      ne(posts.post_status, 'trash') // Exclude trashed posts
    ];

    // Exclude current post when editing
    if (excludePostId) {
      conditions.push(ne(posts.ID, excludePostId));
    }

    const existingPost = await db
      .select({ ID: posts.ID })
      .from(posts)
      .where(and(...conditions))
      .limit(1);

    return existingPost.length > 0;
  } catch (error) {
    console.error('Error checking post slug existence:', error);
    return false;
  }
}

/**
 * Generate a unique slug for posts
 * This function requires database access and should only be used server-side
 */
export async function generateUniquePostSlug(title: string, excludePostId?: number): Promise<string> {
  const baseSlug = generateSlug(title);
  let slug = baseSlug;
  let counter = 1;

  // Keep checking until we find a unique slug
  while (await checkPostSlugExists(slug, excludePostId)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}
