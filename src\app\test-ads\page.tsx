'use client';

import { useState } from 'react';
import Layout from '@/components/layout/layout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import AdverticaAdRenderer from '@/components/ads/advertica-ad-renderer';

export default function TestAdsPage() {
  const [adCode, setAdCode] = useState(`<ins style="width: 0px;height:0px" data-width="0" data-height="0" class="oeb87bc4eba" data-domain="//data684.click" data-affquery="/3961710632ccc573a0a0/eb87bc4eba/?placementName=default"><script src="//data684.click/js/responsive.js" async></script></ins>`);
  const [showAd, setShowAd] = useState(false);

  const testImageCode = `<a href="https://imgbb.com/"><img src="https://i.ibb.co.com/RTgCj5Qm/Add-a-heading.png" alt="Add-a-heading" border="0"></a>`;

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-8">Ad Code Testing Page</h1>
        
        <div className="space-y-8">
          {/* Test Image Code */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-green-800">✅ Working Image Code (Reference)</h2>
            <div className="mb-4">
              <code className="text-sm bg-gray-100 p-2 rounded block overflow-x-auto">
                {testImageCode}
              </code>
            </div>
            <div className="border border-gray-300 rounded p-4 bg-white">
              <div dangerouslySetInnerHTML={{ __html: testImageCode }} />
            </div>
          </div>

          {/* Test Ad Code Input */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-800">🧪 Test Your Ad Code</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Ad Code:</label>
                <Textarea
                  value={adCode}
                  onChange={(e) => setAdCode(e.target.value)}
                  className="font-mono text-sm"
                  rows={4}
                  placeholder="Paste your ad code here..."
                />
              </div>
              
              <Button 
                onClick={() => setShowAd(!showAd)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {showAd ? 'Hide Ad' : 'Show Ad'}
              </Button>
            </div>
          </div>

          {/* Ad Display Area */}
          {showAd && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-yellow-800">📺 Advertisement Preview</h2>

              <div className="border border-gray-300 rounded p-4 bg-white min-h-[100px]">
                <AdverticaAdRenderer adCode={adCode} />
              </div>
            </div>
          )}



          {/* Instructions */}
          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-indigo-800">📋 How to Use</h2>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Make sure you have ad permissions (use the debug panel if needed)</li>
              <li>Paste your advertisement code in the textarea above</li>
              <li>Click "Show Ad" to preview the advertisement</li>
              <li>The ad will be displayed with proper formatting and functionality</li>
            </ol>
          </div>
        </div>
      </div>

    </Layout>
  );
}
