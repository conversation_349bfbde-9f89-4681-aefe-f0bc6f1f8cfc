'use client';

import { useState } from 'react';
import Layout from '@/components/layout/layout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import AdverticaAdRenderer from '@/components/ads/advertica-ad-renderer';
import SmartAdRenderer from '@/components/ads/smart-ad-renderer';
import AdDebugPanel from '@/components/debug/ad-debug-panel';

export default function TestAdsPage() {
  const [adCode, setAdCode] = useState(`<ins style="width: 0px;height:0px" data-width="0" data-height="0" class="oeb87bc4eba" data-domain="//data684.click" data-affquery="/3961710632ccc573a0a0/eb87bc4eba/?placementName=default"><script src="//data684.click/js/responsive.js" async></script></ins>`);
  const [showAd, setShowAd] = useState(false);

  const testImageCode = `<a href="https://imgbb.com/"><img src="https://i.ibb.co.com/RTgCj5Qm/Add-a-heading.png" alt="Add-a-heading" border="0"></a>`;

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-8">Ad Code Testing Page</h1>
        
        <div className="space-y-8">
          {/* Test Image Code */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-green-800">✅ Working Image Code (Reference)</h2>
            <div className="mb-4">
              <code className="text-sm bg-gray-100 p-2 rounded block overflow-x-auto">
                {testImageCode}
              </code>
            </div>
            <div className="border border-gray-300 rounded p-4 bg-white">
              <div dangerouslySetInnerHTML={{ __html: testImageCode }} />
            </div>
          </div>

          {/* Test Ad Code Input */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-800">🧪 Test Your Ad Code</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Ad Code:</label>
                <Textarea
                  value={adCode}
                  onChange={(e) => setAdCode(e.target.value)}
                  className="font-mono text-sm"
                  rows={4}
                  placeholder="Paste your ad code here..."
                />
              </div>
              
              <Button 
                onClick={() => setShowAd(!showAd)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {showAd ? 'Hide Ad' : 'Show Ad'}
              </Button>
            </div>
          </div>

          {/* Ad Display Area */}
          {showAd && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-yellow-800">📺 Ad Display Area</h2>
              
              {/* Regular dangerouslySetInnerHTML method */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Method 1: Regular HTML Rendering</h3>
                <div className="ad-container border border-gray-300 rounded p-4 bg-white min-h-[100px]">
                  <div dangerouslySetInnerHTML={{ __html: adCode }} />
                </div>
              </div>

              {/* Advertica specific renderer */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Method 2: Advertica Specific Renderer</h3>
                <div className="border border-gray-300 rounded p-4 bg-white min-h-[100px]">
                  <AdverticaAdRenderer adCode={adCode} />
                </div>
              </div>

              {/* Smart renderer with CORS handling */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Method 3: Smart Renderer (CORS Safe)</h3>
                <div className="border border-gray-300 rounded p-4 bg-white min-h-[100px]">
                  <SmartAdRenderer adCode={adCode} />
                </div>
              </div>
            </div>
          )}

          {/* Debug Information */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">🔍 Debug Information</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Ad Code Length:</strong> {adCode.length} characters</p>
              <p><strong>Contains data684.click:</strong> {adCode.includes('data684.click') ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Contains script tag:</strong> {adCode.includes('<script') ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Contains ins tag:</strong> {adCode.includes('<ins') ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Contains data-domain:</strong> {adCode.includes('data-domain') ? '✅ Yes' : '❌ No'}</p>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-indigo-800">📋 Testing Instructions</h2>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>First, make sure you have ad permissions (use the debug panel)</li>
              <li>Paste your Advertica ad code in the textarea above</li>
              <li>Click "Show Ad" to test both rendering methods</li>
              <li>Open browser console (F12) to see debug messages</li>
              <li>Check the Network tab to see if scripts are loading</li>
              <li>Use the debug panel to analyze ad containers</li>
            </ol>
          </div>
        </div>
      </div>

      {/* Debug Panel */}
      <AdDebugPanel />
    </Layout>
  );
}
