'use client';

import { useEffect, useRef } from 'react';

interface AdverticaAdRendererProps {
  adCode: string;
  className?: string;
}

/**
 * Specialized renderer for Advertica ads
 * Handles the specific requirements of Advertica ad network
 */
export default function AdverticaAdRenderer({ adCode, className = '' }: AdverticaAdRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || !adCode) return;

    const container = containerRef.current;
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Set the ad code
    container.innerHTML = adCode;

    // Process Advertica ads specifically
    const processAdverticaAd = () => {
      const insElements = container.querySelectorAll('ins[data-domain]');
      const scripts = container.querySelectorAll('script');



      // Process each ins element
      insElements.forEach((ins, index) => {


        // Ensure the ins element is visible and has proper dimensions
        const insElement = ins as HTMLElement;
        insElement.style.display = 'block';
        insElement.style.minHeight = '250px'; // Give it a reasonable height
        insElement.style.minWidth = '300px';  // Give it a reasonable width
        insElement.style.backgroundColor = '#f0f0f0'; // Temporary background to see if it's there
        insElement.style.border = '1px solid #ccc'; // Temporary border
        insElement.style.position = 'relative';

        // Add some placeholder content to verify the element is working
        if (!insElement.innerHTML.includes('script')) {
          insElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Loading Advertica Ad...</div>';
        }
      });

      // Process scripts
      scripts.forEach((script, index) => {

        
        if (script.src) {
          const newScript = document.createElement('script');
          let src = script.src;
          
          // Handle protocol-relative URLs
          if (src.startsWith('//')) {
            src = `https:${src}`;
          }
          
          newScript.src = src;
          newScript.async = true;
          
          // Copy attributes
          Array.from(script.attributes).forEach(attr => {
            if (attr.name !== 'src') {
              newScript.setAttribute(attr.name, attr.value);
            }
          });

          newScript.onload = () => {
            // Trigger initialization after script loads
            setTimeout(() => {
              // Some ad networks need DOM events
              window.dispatchEvent(new Event('load'));
              window.dispatchEvent(new Event('DOMContentLoaded'));

              // Remove placeholder styling once ad loads
              const updatedIns = container.querySelectorAll('ins[data-domain]');
              updatedIns.forEach(ins => {
                const insElement = ins as HTMLElement;
                insElement.style.backgroundColor = '';
                insElement.style.border = '';
              });

              // Try to manually trigger Advertica initialization
              if (typeof window !== 'undefined') {
                // Look for any global Advertica functions
                const globalKeys = Object.keys(window);
                const adverticaKeys = globalKeys.filter(key =>
                  key.toLowerCase().includes('advertica') ||
                  key.toLowerCase().includes('responsive')
                );

                // Try to call any initialization functions
                adverticaKeys.forEach(key => {
                  try {
                    const func = (window as any)[key];
                    if (typeof func === 'function') {
                      func();
                    }
                  } catch (e) {
                    // Silently handle errors
                  }
                });
              }
            }, 1000);
          };

          newScript.onerror = (error) => {
            // Try with HTTP if HTTPS failed
            if (src.startsWith('https://')) {
              const httpSrc = src.replace('https:', 'http:');

              const httpScript = document.createElement('script');
              httpScript.src = httpSrc;
              httpScript.async = true;

              document.head.appendChild(httpScript);
            }
          };

          // Replace the original script
          script.parentNode?.replaceChild(newScript, script);
        }
      });
    };

    // Process the ad after a short delay
    setTimeout(processAdverticaAd, 100);

    // Cleanup function
    return () => {
      // Remove any scripts we added to prevent memory leaks
      const addedScripts = document.head.querySelectorAll('script[src*="data684.click"]');
      addedScripts.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      });
    };
  }, [adCode]);

  return (
    <div 
      ref={containerRef}
      className={`advertica-ad-container ${className}`}
      style={{
        minHeight: '50px',
        minWidth: '50px',
        display: 'block',
        position: 'relative'
      }}
    />
  );
}
