'use client';

import { useEffect, useRef } from 'react';

interface AdverticaAdRendererProps {
  adCode: string;
  className?: string;
}

/**
 * Specialized renderer for Advertica ads
 * Handles the specific requirements of Advertica ad network
 */
export default function AdverticaAdRenderer({ adCode, className = '' }: AdverticaAdRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || !adCode) return;

    const container = containerRef.current;
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Set the ad code
    container.innerHTML = adCode;

    // Process Advertica ads specifically
    const processAdverticaAd = () => {
      const insElements = container.querySelectorAll('ins[data-domain]');
      const scripts = container.querySelectorAll('script');

      console.log('Advertica Debug:', {
        insElements: insElements.length,
        scripts: scripts.length,
        adCode: adCode
      });

      // Process each ins element
      insElements.forEach((ins, index) => {
        console.log(`Processing ins element ${index + 1}:`, {
          dataDomain: ins.getAttribute('data-domain'),
          dataAffquery: ins.getAttribute('data-affquery'),
          className: ins.className
        });

        // Ensure the ins element is visible and has proper dimensions
        const insElement = ins as HTMLElement;
        insElement.style.display = 'block';
        insElement.style.minHeight = '250px'; // Give it a reasonable height
        insElement.style.minWidth = '300px';  // Give it a reasonable width
        insElement.style.backgroundColor = '#f0f0f0'; // Temporary background to see if it's there
        insElement.style.border = '1px solid #ccc'; // Temporary border
        insElement.style.position = 'relative';

        // Add some placeholder content to verify the element is working
        if (!insElement.innerHTML.includes('script')) {
          insElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Loading Advertica Ad...</div>';
        }
      });

      // Process scripts
      scripts.forEach((script, index) => {
        console.log(`Processing script ${index + 1}:`, script.src);
        
        if (script.src) {
          const newScript = document.createElement('script');
          let src = script.src;
          
          // Handle protocol-relative URLs
          if (src.startsWith('//')) {
            src = `https:${src}`;
          }
          
          newScript.src = src;
          newScript.async = true;
          
          // Copy attributes
          Array.from(script.attributes).forEach(attr => {
            if (attr.name !== 'src') {
              newScript.setAttribute(attr.name, attr.value);
            }
          });

          newScript.onload = () => {
            console.log('Advertica script loaded successfully:', src);

            // Trigger initialization after script loads
            setTimeout(() => {
              // Some ad networks need DOM events
              window.dispatchEvent(new Event('load'));
              window.dispatchEvent(new Event('DOMContentLoaded'));

              // Check if the ad network has initialized
              const updatedIns = container.querySelectorAll('ins[data-domain]');
              updatedIns.forEach(ins => {
                console.log('Post-load ins element:', ins.outerHTML);

                // Remove placeholder styling once ad loads
                const insElement = ins as HTMLElement;
                insElement.style.backgroundColor = '';
                insElement.style.border = '';
              });

              // Try to manually trigger Advertica initialization
              if (typeof window !== 'undefined') {
                // Look for any global Advertica functions
                const globalKeys = Object.keys(window);
                const adverticaKeys = globalKeys.filter(key =>
                  key.toLowerCase().includes('advertica') ||
                  key.toLowerCase().includes('responsive')
                );

                console.log('Found potential Advertica globals:', adverticaKeys);

                // Try to call any initialization functions
                adverticaKeys.forEach(key => {
                  try {
                    const func = (window as any)[key];
                    if (typeof func === 'function') {
                      console.log('Calling Advertica function:', key);
                      func();
                    }
                  } catch (e) {
                    console.log('Failed to call function:', key, e);
                  }
                });
              }
            }, 1000); // Increased delay for better initialization
          };

          newScript.onerror = (error) => {
            console.error('Failed to load Advertica script:', src, error);
            
            // Try with HTTP if HTTPS failed
            if (src.startsWith('https://')) {
              const httpSrc = src.replace('https:', 'http:');
              console.log('Retrying with HTTP:', httpSrc);
              
              const httpScript = document.createElement('script');
              httpScript.src = httpSrc;
              httpScript.async = true;
              
              httpScript.onload = () => {
                console.log('Advertica script loaded with HTTP:', httpSrc);
              };
              
              httpScript.onerror = () => {
                console.error('Failed to load Advertica script with both HTTPS and HTTP');
              };
              
              document.head.appendChild(httpScript);
            }
          };

          // Replace the original script
          script.parentNode?.replaceChild(newScript, script);
        }
      });
    };

    // Process the ad after a short delay
    setTimeout(processAdverticaAd, 100);

    // Cleanup function
    return () => {
      // Remove any scripts we added to prevent memory leaks
      const addedScripts = document.head.querySelectorAll('script[src*="data684.click"]');
      addedScripts.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      });
    };
  }, [adCode]);

  return (
    <div 
      ref={containerRef}
      className={`advertica-ad-container ${className}`}
      style={{
        minHeight: '50px',
        minWidth: '50px',
        display: 'block',
        position: 'relative'
      }}
    />
  );
}
