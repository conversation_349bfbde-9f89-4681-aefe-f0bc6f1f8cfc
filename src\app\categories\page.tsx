'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/layout';
import Link from 'next/link';

interface Category {
  id: number;
  term_id: number;
  name: string;
  slug: string;
  description: string;
  count: number;
  parent: number;
}

const CategoriesPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name'>('name');

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    let filtered = categories;

    // Apply search filter
    if (searchTerm.trim() !== '') {
      filtered = categories.filter(category =>
        decodeHtmlEntities(category.name).toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting - always sort by name
    const sorted = [...filtered].sort((a, b) => {
      return decodeHtmlEntities(a.name).localeCompare(decodeHtmlEntities(b.name));
    });

    setFilteredCategories(sorted);
  }, [categories, searchTerm, sortBy]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories?include_empty=false');
      const result = await response.json();

      if (result.success) {
        setCategories(result.data);
        setFilteredCategories(result.data);
      } else {
        setError(result.error || 'Failed to fetch categories');
      }
    } catch (err) {
      setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Get emoji for category
  const getCategoryEmoji = (name: string) => {
    const emojiMap: { [key: string]: string } = {
      'Technology': '💻',
      'Health': '🏥',
      'Travel': '✈️',
      'Food': '🍽️',
      'Lifestyle': '🌟',
      'Sports': '⚽',
      'Music': '🎵',
      'Art': '🎨',
      'Science': '🔬',
      'Business': '💼',
      'Education': '📚',
      'Fashion': '👗',
      'Gaming': '🎮',
      'Photography': '📸',
      'Fitness': '💪',
      'Cooking': '👨‍🍳',
      'Nature': '🌿',
      'Movies': '🎬',
      'Books': '📖',
      'News': '📰',
      'Programming': '⌨️',
      'Web Development': '🌐',
      'Mobile Apps': '📱',
      'AI & Machine Learning': '🤖',
      'DevOps': '⚙️',
      'Design': '🎨',
      'Marketing': '📈',
      'Finance': '💰',
      'Politics': '🏛️',
      'Environment': '🌍',
    };

    return emojiMap[name] || '📁';
  };

  // Decode HTML entities
  const decodeHtmlEntities = (text: string) => {
    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ');
  };

  // Get color for category based on name
  const getCategoryColor = (name: string) => {
    const colorMap: { [key: string]: string } = {
      'Technology': 'bg-blue-500',
      'Programming': 'bg-green-500',
      'Web Development': 'bg-purple-500',
      'Mobile Apps': 'bg-orange-500',
      'AI & Machine Learning': 'bg-red-500',
      'DevOps': 'bg-indigo-500',
      'Design': 'bg-pink-500',
      'Business': 'bg-gray-500',
      'Health': 'bg-emerald-500',
      'Travel': 'bg-cyan-500',
      'Food': 'bg-yellow-500',
      'Lifestyle': 'bg-rose-500',
      'Sports': 'bg-lime-500',
      'Music': 'bg-violet-500',
      'Art': 'bg-fuchsia-500',
      'Science': 'bg-teal-500',
      'Education': 'bg-amber-500',
      'Fashion': 'bg-sky-500',
      'Gaming': 'bg-slate-500',
      'Photography': 'bg-stone-500',
      'Fitness': 'bg-zinc-500',
      'Cooking': 'bg-neutral-500',
      'Nature': 'bg-green-600',
      'Movies': 'bg-red-600',
      'Books': 'bg-blue-600',
      'News': 'bg-gray-600',
    };

    // If no specific color, generate one based on name hash
    if (!colorMap[name]) {
      const colors = [
        'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500',
        'bg-red-500', 'bg-indigo-500', 'bg-pink-500', 'bg-cyan-500',
        'bg-emerald-500', 'bg-yellow-500', 'bg-rose-500', 'bg-lime-500'
      ];
      const hash = name.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
      return colors[hash % colors.length];
    }

    return colorMap[name];
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Categories
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
                Explore our content organized by topics and interests
              </p>

              {/* Search and Sort Controls */}
              <div className="max-w-2xl mx-auto">
                <div className="flex flex-col sm:flex-row gap-4 items-center">
                  {/* Search Box */}
                  <div className="flex-1 w-full">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        placeholder="Search categories..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  </div>


                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Results Counter */}
          {!loading && !error && (
            <div className="mb-8 text-center">
              <p className="text-gray-600">
                {searchTerm ? (
                  <>
                    Found <span className="font-semibold">{filteredCategories.length}</span> categories
                    {searchTerm && (
                      <>
                        {' '}matching "<span className="font-semibold">{searchTerm}</span>"
                        <button
                          onClick={() => setSearchTerm('')}
                          className="ml-2 text-indigo-600 hover:text-indigo-700 text-sm underline"
                        >
                          Clear search
                        </button>
                      </>
                    )}
                  </>
                ) : (
                  <>
                    Showing <span className="font-semibold">{categories.length}</span> categories
                  </>
                )}
              </p>
            </div>
          )}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
                    <div className="h-32 bg-gray-200"></div>
                    <div className="p-6">
                      <div className="h-4 bg-gray-200 rounded mb-4"></div>
                      <div className="h-3 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="text-red-500 text-lg mb-4">
                ⚠️ Error loading categories
              </div>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={fetchCategories}
                className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-gray-500 text-lg mb-4">
                {searchTerm ? '🔍 No categories found' : '📁 No categories found'}
              </div>
              <p className="text-gray-600">
                {searchTerm
                  ? `No categories match "${searchTerm}". Try a different search term.`
                  : 'Categories will appear here once they are created.'
                }
              </p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="mt-4 bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Clear Search
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
              {filteredCategories.map((category) => (
                <Link
                  key={category.id}
                  href={`/search?category=${category.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-gray-200 group-hover:scale-105 transform">
                    <div className={`h-32 ${getCategoryColor(category.name)} relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute top-4 left-6">
                        <span className="text-3xl transform group-hover:scale-110 transition-transform duration-300 inline-block">
                          {getCategoryEmoji(category.name)}
                        </span>
                      </div>
                      <div className="absolute bottom-4 left-6 right-6">
                        <h3 className="text-2xl font-bold text-white mb-1 group-hover:text-gray-100 transition-colors">
                          {decodeHtmlEntities(category.name)}
                        </h3>
                      </div>
                    </div>

                    <div className="p-6">
                      <p className="text-gray-600 mb-4 leading-relaxed line-clamp-2">
                        {category.description || 'Explore posts in this category'}
                      </p>

                      <div className="flex items-center justify-end">
                        <div className="flex items-center text-indigo-600 group-hover:text-indigo-700 transition-colors">
                          <span className="text-sm font-medium mr-2">View Posts</span>
                          <svg
                            className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* Statistics Section */}
        {!loading && !error && categories.length > 0 && (
          <div className="bg-gray-50 border-t border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600 mb-2">
                    {categories.length}
                  </div>
                  <div className="text-gray-600">Total Categories</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {categories.reduce((sum, cat) => sum + cat.count, 0)}
                  </div>
                  <div className="text-gray-600">Total Posts</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {Math.round(categories.reduce((sum, cat) => sum + cat.count, 0) / categories.length)}
                  </div>
                  <div className="text-gray-600">Avg Posts per Category</div>
                </div>
              </div>
            </div>
          </div>
        )}


      </div>
    </Layout>
  );
};

export default CategoriesPage;
