/**
 * Test utilities for post deletion functionality
 * This file contains test functions to verify image deletion logic
 */

import { extractPublicId } from '@/lib/cloudinary';
import { extractImagesFromContent } from '@/lib/utils/content-processor';

/**
 * Test function to verify Cloudinary public ID extraction
 */
export function testPublicIdExtraction() {
  const testUrls = [
    'https://res.cloudinary.com/demo/image/upload/v1234567890/wikify-blog/user123/sample-image.jpg',
    'https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/another-image.png',
    'https://res.cloudinary.com/demo/image/upload/v1/wikify-blog/user456/test.webp',
    'https://example.com/regular-image.jpg', // Should return null
    'wikify-blog/user123/direct-public-id', // Direct public ID
  ];

  console.log('Testing Cloudinary public ID extraction:');
  testUrls.forEach(url => {
    const publicId = extractPublicId(url);
    console.log(`URL: ${url}`);
    console.log(`Public ID: ${publicId}`);
    console.log('---');
  });
}

/**
 * Test function to verify image extraction from content
 */
export function testImageExtractionFromContent() {
  const testContent = `
    <p>This is a test post with images.</p>
    <img src="https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/image1.jpg" alt="Test Image 1" />
    <p>Some more content here.</p>
    <img src="/uploads/local-image.png" alt="Local Image" />
    <p>Another paragraph.</p>
    <img src="https://res.cloudinary.com/demo/image/upload/v1234/wikify-blog/user123/image2.webp" alt="Test Image 2" />
  `;

  console.log('Testing image extraction from content:');
  const images = extractImagesFromContent(testContent);
  console.log('Extracted images:', images);
  
  // Filter Cloudinary images
  const cloudinaryImages = images.filter(url => url && url.includes('cloudinary.com'));
  console.log('Cloudinary images:', cloudinaryImages);
  
  // Extract public IDs
  const publicIds = cloudinaryImages.map(url => extractPublicId(url)).filter(Boolean);
  console.log('Public IDs:', publicIds);
}

/**
 * Test function to simulate the deletion process
 */
export function testDeletionProcess(postContent: string, featuredImage: string | null, userId: string) {
  console.log('Testing deletion process simulation:');
  console.log('User ID:', userId);
  console.log('Featured Image:', featuredImage);
  
  const imagesToDelete: string[] = [];
  
  // Add featured image if exists
  if (featuredImage) {
    imagesToDelete.push(featuredImage);
  }
  
  // Extract images from content
  const contentImages = extractImagesFromContent(postContent);
  imagesToDelete.push(...contentImages);
  
  console.log('All images to process:', imagesToDelete);
  
  // Filter Cloudinary images
  const cloudinaryImages = imagesToDelete.filter(url => 
    url && url.includes('cloudinary.com')
  );
  
  console.log('Cloudinary images to delete:', cloudinaryImages);
  
  // Extract public IDs and check ownership
  const ownedPublicIds: string[] = [];
  cloudinaryImages.forEach(url => {
    const publicId = extractPublicId(url);
    if (publicId && publicId.includes(userId)) {
      ownedPublicIds.push(publicId);
    } else if (publicId) {
      console.warn(`Skipping deletion of image ${publicId} - user ${userId} doesn't own it`);
    }
  });
  
  console.log('Public IDs owned by user:', ownedPublicIds);
  
  return {
    totalImages: imagesToDelete.length,
    cloudinaryImages: cloudinaryImages.length,
    ownedImages: ownedPublicIds.length,
    publicIds: ownedPublicIds
  };
}

/**
 * Run all tests
 */
export function runAllTests() {
  console.log('=== Running Post Deletion Tests ===\n');
  
  testPublicIdExtraction();
  console.log('\n');
  
  testImageExtractionFromContent();
  console.log('\n');
  
  // Test with sample data
  const sampleContent = `
    <h1>Sample Post</h1>
    <p>This is a sample post with multiple images.</p>
    <img src="https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/hero-image.jpg" alt="Hero" />
    <p>Some content here.</p>
    <img src="https://res.cloudinary.com/demo/image/upload/v1/wikify-blog/user456/other-user-image.png" alt="Other User" />
    <img src="/uploads/local-file.jpg" alt="Local" />
    <img src="https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/gallery-1.webp" alt="Gallery 1" />
  `;
  
  const sampleFeaturedImage = 'https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/featured.jpg';
  const userId = 'user123';
  
  const result = testDeletionProcess(sampleContent, sampleFeaturedImage, userId);
  console.log('Deletion simulation result:', result);
  
  console.log('\n=== Tests Complete ===');
}
