import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, passwordResetTokens } from '@/lib/db/schema';
import { eq, and, gt } from 'drizzle-orm';
import { sendPasswordResetEmail } from '@/lib/utils/email';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Check if user exists with this email
    const existingUser = await db
      .select({
        ID: users.ID,
        user_login: users.user_login,
        user_email: users.user_email,
        display_name: users.display_name,
        user_status: users.user_status
      })
      .from(users)
      .where(eq(users.user_email, email.toLowerCase()))
      .limit(1);

    if (existingUser.length === 0) {
      // For security reasons, don't reveal if email exists or not
      // Always return success message
      return NextResponse.json({
        success: true,
        message: 'If an account with this email exists, you will receive a password reset link shortly.'
      });
    }

    const user = existingUser[0];

    // Check if user account is approved (only approved users can reset password)
    if (user.user_status !== 1) { // 1 = APPROVED
      return NextResponse.json({
        success: true,
        message: 'If an account with this email exists, you will receive a password reset link shortly.'
      });
    }

    // Check for existing valid tokens (prevent spam)
    const now = new Date();
    const existingTokens = await db
      .select()
      .from(passwordResetTokens)
      .where(
        and(
          eq(passwordResetTokens.email, email.toLowerCase()),
          eq(passwordResetTokens.used, false),
          gt(passwordResetTokens.expires, now)
        )
      );

    if (existingTokens.length > 0) {
      // If there's already a valid token created in the last 5 minutes, don't create a new one
      const recentToken = existingTokens.find(token => {
        const timeDiff = now.getTime() - token.created_at.getTime();
        return timeDiff < 5 * 60 * 1000; // 5 minutes
      });

      if (recentToken) {
        return NextResponse.json({
          success: true,
          message: 'A password reset link was recently sent to your email. Please check your inbox or wait a few minutes before requesting again.'
        });
      }
    }

    // Generate secure random token
    const resetToken = crypto.randomBytes(32).toString('hex');
    
    // Set expiration time (1 hour from now)
    const expiresAt = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour

    // Save reset token to database
    await db.insert(passwordResetTokens).values({
      email: email.toLowerCase(),
      token: resetToken,
      expires: expiresAt,
      used: false,
      created_at: now,
    });

    // Send password reset email
    const emailSent = await sendPasswordResetEmail(
      email,
      user.display_name || user.user_login,
      resetToken
    );

    if (!emailSent) {
      console.error('Failed to send password reset email to:', email);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to send password reset email. Please try again later or contact support.' 
        },
        { status: 500 }
      );
    }

    console.log('Password reset email sent successfully to:', email);

    return NextResponse.json({
      success: true,
      message: 'Password reset link has been sent to your email address. Please check your inbox and follow the instructions.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
