# Wikify Blog - Multi-Author Blogging Platform

A modern, high-performance multi-author blogging platform built with Next.js 15, preserving all your existing WordPress data while providing a superior user experience.

## 🚀 Features

### ✅ **Complete WordPress Data Preservation**
- **All existing users** with original usernames, emails, and passwords
- **All blog posts** with content, images, categories, and tags
- **All comments** with threading and approval status
- **All media files** automatically served from existing uploads folder
- **User roles and permissions** (Administrator, Editor, Author)

### 🔧 **Modern Technology Stack**
- **Next.js 15** with App Router for optimal performance
- **TypeScript** for type safety and better development experience
- **Drizzle ORM** for efficient database operations
- **NextAuth.js** for secure authentication
- **Cloudinary** for new image uploads and optimization
- **TailwindCSS** for responsive, modern UI design

### 📝 **Content Management**
- **Rich text editor** with TipTap for creating and editing posts
- **Ad Network Integration** - Insert ads from Google AdSense, Facebook, Amazon and more
- **Image upload** with Cloudinary integration
- **Draft and publish** workflow
- **Categories and tags** management
- **SEO-friendly** URLs and meta tags

### 💬 **Community Features**
- **Comment system** with threading and moderation
- **User profiles** and author pages
- **Search functionality** across all content
- **Responsive design** for all devices

## 🛠 Installation & Setup

### Prerequisites
- Node.js 18+ installed
- MySQL database access
- Your existing WordPress database
- Cloudinary account (for new uploads)

### 1. Clone and Install
```bash
git clone <repository-url>
cd wikify
npm install
```

### 2. Environment Configuration
Copy `.env.example` to `.env.local` and update with your credentials:

```env
# Database (your existing WordPress database)
DATABASE_HOST=your_host
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database_name
DATABASE_PORT=3306

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# Cloudinary (for new uploads)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### 3. Verify Data Migration
```bash
npm run dev
```

Visit `http://localhost:3000/test` to verify all your data is accessible.

### 4. Test Authentication
Visit `http://localhost:3000/login` and login with any existing WordPress credentials.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── blog/              # Blog listing page
│   ├── post/[slug]/       # Individual post pages
│   ├── dashboard/         # User dashboard
│   ├── admin/             # Admin panel
│   └── search/            # Search page
├── components/            # Reusable React components
│   ├── ui/                # Basic UI components
│   ├── forms/             # Form components
│   ├── layout/            # Layout components
│   └── blog/              # Blog-specific components
├── lib/                   # Utility libraries
│   ├── db/                # Database schema and connection
│   ├── utils/             # Helper functions
│   ├── auth.ts            # Authentication configuration
│   └── cloudinary.ts      # Cloudinary configuration
└── types/                 # TypeScript type definitions

uploads/                   # Your existing WordPress media files
```

## 🔐 Authentication

The application uses WordPress-compatible authentication:

- **Login with existing credentials**: Users can sign in with their WordPress username or email
- **Password compatibility**: Original WordPress password hashes are supported
- **Role preservation**: Admin, Editor, and Author roles are maintained
- **Session management**: Secure JWT-based sessions with NextAuth.js

## 📊 API Endpoints

### Posts
- `GET /api/posts` - List posts with pagination and filtering
- `GET /api/posts/[id]` - Get single post by ID
- `GET /api/posts/by-slug/[slug]` - Get post by slug
- `POST /api/posts` - Create new post (authenticated)
- `PUT /api/posts/[id]` - Update post (authenticated)
- `DELETE /api/posts/[id]` - Delete post (authenticated)

### Comments
- `GET /api/comments?post_id=[id]` - Get comments for a post
- `POST /api/comments` - Create new comment

### Search
- `GET /api/search?q=[query]` - Search posts with filters

### Media
- `POST /api/upload` - Upload new images to Cloudinary
- `GET /uploads/[...path]` - Serve existing WordPress media files

### Data Verification
- `GET /api/verify-data` - Check database connection and data counts

## 🎨 User Interface

### Public Pages
- **Home page** with feature overview and quick links
- **Blog listing** with search, filtering, and pagination
- **Individual post pages** with comments
- **Search results** with advanced filtering
- **Author profiles** and post archives

### User Dashboard
- **Personal dashboard** with post statistics
- **Create/edit posts** with rich text editor
- **Profile management** with avatar upload
- **Post management** with draft/publish workflow

### Admin Panel
- **Site overview** with statistics and recent activity
- **User management** (Admin/Editor only)
- **Content moderation** and bulk operations
- **System health** monitoring

## 🔍 Search & Filtering

- **Full-text search** across post titles, content, and excerpts
- **Category filtering** with post counts
- **Tag filtering** with visual tag cloud
- **Author filtering** for multi-author blogs
- **Date range filtering** for time-based searches
- **Pagination** for large result sets

## 💾 Data Preservation

### What's Preserved
✅ **Users**: All accounts, roles, and metadata
✅ **Posts**: Content, titles, slugs, dates, and status
✅ **Comments**: All comments with threading and approval status
✅ **Media**: All images and files from uploads folder
✅ **Categories**: Hierarchical structure and relationships
✅ **Tags**: All tags and post associations
✅ **Metadata**: Custom fields and additional data

### WordPress Compatibility
- **Database schema**: Uses existing WordPress table structure
- **URL structure**: Maintains SEO-friendly post URLs
- **Image paths**: Automatically processes WordPress image URLs
- **Content formatting**: Preserves HTML formatting and embedded media

## 🚀 Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

Visit [http://localhost:3000](http://localhost:3000) to see your blog in action.

## 📈 Performance

- **Server-side rendering** for optimal SEO and loading speed
- **Image optimization** with Next.js Image component
- **Database connection pooling** for efficient queries
- **Caching strategies** for frequently accessed content
- **Responsive images** with automatic format selection

## 🔧 Customization

### Styling
- Built with **TailwindCSS** for easy customization
- **Component-based architecture** for reusable UI elements
- **Dark mode support** (can be enabled)
- **Custom themes** through CSS variables

### Functionality
- **Plugin system** for extending features
- **Custom post types** support
- **Additional metadata** fields
- **Third-party integrations** (analytics, SEO tools)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Documentation**: Check the SETUP.md file for detailed setup instructions
- **Issues**: Report bugs and request features on GitHub Issues
- **Data Verification**: Use `/test` page to verify your WordPress data migration

## 🙏 Acknowledgments

- **WordPress** for the original platform and data structure
- **Next.js team** for the amazing React framework
- **Drizzle team** for the excellent TypeScript ORM
- **TailwindCSS** for the utility-first CSS framework
#   w i k i f y 
 
 