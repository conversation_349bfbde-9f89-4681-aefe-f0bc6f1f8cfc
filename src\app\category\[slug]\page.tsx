'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Layout from '@/components/layout/layout';
import PostCard from '@/components/blog/post-card';
import Link from 'next/link';

interface Post {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  status: string;
  date: string;
  modified: string;
  author: {
    id: number;
    name: string;
    username: string;
    email: string;
  };
  commentCount: number;
  featured_image?: string;
  categories: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  count: number;
  parent: number;
}

export default function CategoryPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [category, setCategory] = useState<Category | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    if (slug) {
      fetchCategoryAndPosts();
    }
  }, [slug]);

  const fetchCategoryAndPosts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch posts for this category
      const postsResponse = await fetch(`/api/posts?category=${slug}&limit=12&page=1`);
      const postsResult = await postsResponse.json();

      if (postsResult.success) {
        setPosts(postsResult.data);
        setHasMore(postsResult.pagination.page < postsResult.pagination.totalPages);
        
        // If we have posts, get category info from the first post
        if (postsResult.data.length > 0) {
          const firstPost = postsResult.data[0];
          const categoryInfo = firstPost.categories?.find((cat: any) => cat.slug === slug);
          if (categoryInfo) {
            setCategory({
              id: categoryInfo.id,
              name: categoryInfo.name,
              slug: categoryInfo.slug,
              description: '',
              count: postsResult.pagination.total,
              parent: 0
            });
          }
        } else {
          // Try to fetch category info directly
          const categoriesResponse = await fetch('/api/categories');
          const categoriesResult = await categoriesResponse.json();
          if (categoriesResult.success) {
            const foundCategory = categoriesResult.data.find((cat: Category) => cat.slug === slug);
            if (foundCategory) {
              setCategory(foundCategory);
            }
          }
        }
      } else {
        setError(postsResult.error || 'Failed to fetch posts');
      }
    } catch (err) {
      setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const loadMorePosts = async () => {
    if (loadingMore || !hasMore) return;

    try {
      setLoadingMore(true);
      const nextPage = page + 1;
      
      const response = await fetch(`/api/posts?category=${slug}&limit=12&page=${nextPage}`);
      const result = await response.json();

      if (result.success) {
        setPosts(prev => [...prev, ...result.data]);
        setPage(nextPage);
        setHasMore(nextPage < result.pagination.totalPages);
      }
    } catch (err) {
      console.error('Error loading more posts:', err);
    } finally {
      setLoadingMore(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-6">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <Link href="/categories" className="text-indigo-600 hover:text-indigo-700">
              ← Back to Categories
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <nav className="mb-4">
                <Link href="/categories" className="text-indigo-600 hover:text-indigo-700 text-sm">
                  ← All Categories
                </Link>
              </nav>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {category?.name || 'Category'}
              </h1>
              {category?.description && (
                <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
                  {category.description}
                </p>
              )}

            </div>
          </div>
        </div>

        {/* Posts Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {posts.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {posts.map((post) => (
                  <PostCard key={post.id} post={post} />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center mt-12">
                  <button
                    onClick={loadMorePosts}
                    disabled={loadingMore}
                    className="bg-indigo-600 text-white px-8 py-3 rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loadingMore ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                      </>
                    ) : (
                      'Load More Posts'
                    )}
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-gray-500 text-lg mb-4">📝 No posts found</div>
              <p className="text-gray-600 mb-6">
                There are no posts in this category yet.
              </p>
              <Link
                href="/categories"
                className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Browse Other Categories
              </Link>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
