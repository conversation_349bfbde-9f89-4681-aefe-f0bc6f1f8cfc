/**
 * Utility functions for handling image URLs
 */

/**
 * Get the full image URL for Next.js Image component
 * @param imagePath - The image path from the database
 * @returns Full URL for the image
 */
export function getImageSrc(imagePath: string): string {
  if (!imagePath) return '';

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // If it's a relative path starting with /uploads/, make it absolute
  if (imagePath.startsWith('/uploads/')) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    return `${baseUrl}${imagePath}`;
  }

  // For other relative paths, return as is
  return imagePath;
}

/**
 * Get the absolute URL for meta tags (OpenGraph, Twitter, etc.)
 * @param imagePath - The image path from the database
 * @returns Absolute URL for the image
 */
export function getAbsoluteImageUrl(imagePath: string | null): string | undefined {
  if (!imagePath) return undefined;

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // Get base URL from environment or use localhost as fallback
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

  // If it's a relative path starting with /uploads/, make it absolute
  if (imagePath.startsWith('/uploads/')) {
    return `${baseUrl}${imagePath}`;
  }

  // For other relative paths, prepend base URL
  return `${baseUrl}/${imagePath.replace(/^\/+/, '')}`;
}

/**
 * Generate a default avatar URL based on user's name
 * @param name - User's display name or username
 * @returns URL for a generated avatar
 */
export function getDefaultAvatarUrl(name: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  // Using a simple gradient avatar service or fallback to initials
  const initials = name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
  // You can replace this with any avatar service like Gravatar, UI Avatars, etc.
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=ffffff&size=400&format=png`;
}

/**
 * Check if an image path is valid
 * @param imagePath - The image path to check
 * @returns boolean indicating if the path is valid
 */
export function isValidImagePath(imagePath: string): boolean {
  if (!imagePath) return false;
  
  const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  const extension = imagePath.toLowerCase().split('.').pop();
  
  return validExtensions.includes(`.${extension}`);
}
