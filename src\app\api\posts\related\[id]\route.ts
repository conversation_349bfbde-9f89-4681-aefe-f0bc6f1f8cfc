import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { posts, users, postmeta } from '@/lib/db/schema';
import { eq, ne, desc, and, sql } from 'drizzle-orm';
import { getFeaturedImage } from '@/lib/utils/content-processor';

// GET /api/posts/related/[id] - Get related posts
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params for Next.js 15 compatibility
    const { id } = await params;
    console.log('Related posts API called with ID:', id);
    const postId = parseInt(id);

    // Get pagination parameters from URL
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '6');
    const offset = (page - 1) * limit;

    console.log('Pagination params:', { page, limit, offset });

    if (isNaN(postId)) {
      console.log('Invalid post ID:', id);
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    console.log('Checking if current post exists:', postId);
    // Simplified check - just verify the post exists
    try {
      const currentPost = await db
        .select({ ID: posts.ID })
        .from(posts)
        .where(eq(posts.ID, postId))
        .limit(1);

      console.log('Current post query result:', currentPost);

      if (!currentPost.length) {
        console.log('Post not found:', postId);
        return NextResponse.json(
          { success: false, error: 'Post not found' },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error('Error checking current post:', error);
      return NextResponse.json(
        { success: false, error: 'Database error checking current post' },
        { status: 500 }
      );
    }



    console.log('Fetching related posts...');
    // Get related posts (excluding current post, get latest published posts)
    const relatedPostsData = await db
      .select({
        ID: posts.ID,
        post_title: posts.post_title,
        post_name: posts.post_name,
        post_excerpt: posts.post_excerpt,
        post_content: posts.post_content,
        post_date: posts.post_date,
        author_name: users.display_name
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(
        and(
          ne(posts.ID, postId),
          eq(posts.post_status, 'publish'),
          eq(posts.post_type, 'post')
        )
      )
      .orderBy(desc(posts.post_date))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(
        and(
          ne(posts.ID, postId),
          eq(posts.post_status, 'publish'),
          eq(posts.post_type, 'post')
        )
      );

    const totalCount = totalCountResult[0]?.count || 0;
    const hasMore = (page * limit) < totalCount;

    console.log('Related posts query result:', relatedPostsData?.length || 0, 'posts');
    console.log('Total posts available:', totalCount, 'Has more:', hasMore);

    // Add featured images to each post
    const relatedPosts = await Promise.all(
      relatedPostsData.map(async (post) => {
        try {
          const featured_image = await getFeaturedImage(post.ID, post.post_content || '');
          return {
            ...post,
            featured_image
          };
        } catch (error) {
          console.error(`Error getting featured image for post ${post.ID}:`, error);
          return {
            ...post,
            featured_image: null
          };
        }
      })
    );

    console.log('Returning related posts:', relatedPosts.length);

    return NextResponse.json({
      success: true,
      data: relatedPosts,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore
      }
    });

  } catch (error) {
    console.error('Error fetching related posts:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch related posts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
