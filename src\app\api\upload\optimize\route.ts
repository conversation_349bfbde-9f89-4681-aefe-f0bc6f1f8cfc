import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { optimizeExistingImage, extractPublicId, getImageInfo } from '@/lib/cloudinary';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { imageUrl, options } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { success: false, error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Extract public ID to verify it's a Cloudinary image
    const publicId = extractPublicId(imageUrl);
    if (!publicId) {
      return NextResponse.json(
        { success: false, error: 'Invalid Cloudinary image URL' },
        { status: 400 }
      );
    }

    // Check if user owns the image (basic security check)
    if (!publicId.includes(session.user.id) && !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Get original image info
    const originalInfo = await getImageInfo(publicId);
    if (!originalInfo.success) {
      return NextResponse.json(
        { success: false, error: 'Failed to get original image info' },
        { status: 500 }
      );
    }

    // Generate optimized URL
    const optimizedUrl = optimizeExistingImage(imageUrl, {
      width: options?.width || 1280,
      height: options?.height || 720,
      format: options?.format || 'webp',
      quality: options?.quality || '40',
      crop: options?.crop || 'fill',
    });

    // Calculate estimated compression (rough estimate)
    const estimatedCompression = originalInfo.data.format !== 'webp' ? '20-40' : '10-20';

    return NextResponse.json({
      success: true,
      data: {
        original_url: imageUrl,
        optimized_url: optimizedUrl,
        public_id: publicId,
        original_info: originalInfo.data,
        optimization: {
          width: options?.width || 1280,
          height: options?.height || 720,
          format: options?.format || 'webp',
          quality: options?.quality || '40',
          estimated_compression: estimatedCompression,
        }
      }
    });

  } catch (error) {
    console.error('Optimization error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Optimization failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to get optimization info for an image
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json(
        { success: false, error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const publicId = extractPublicId(imageUrl);
    if (!publicId) {
      return NextResponse.json(
        { success: false, error: 'Invalid Cloudinary image URL' },
        { status: 400 }
      );
    }

    // Get image info
    const imageInfo = await getImageInfo(publicId);
    if (!imageInfo.success) {
      return NextResponse.json(
        { success: false, error: 'Failed to get image info' },
        { status: 500 }
      );
    }

    // Check if image is already optimized
    const isOptimized = imageInfo.data.format === 'webp' &&
                       imageInfo.data.width <= 1280 &&
                       imageInfo.data.height <= 720 &&
                       imageInfo.data.bytes <= 100 * 1024; // Less than 100KB

    return NextResponse.json({
      success: true,
      data: {
        ...imageInfo.data,
        is_optimized: isOptimized,
        optimization_suggestions: isOptimized ? [] : [
          imageInfo.data.format !== 'webp' ? 'Convert to WebP format' : null,
          imageInfo.data.width > 1280 ? 'Resize width to 1280px' : null,
          imageInfo.data.height > 720 ? 'Resize height to 720px' : null,
          imageInfo.data.bytes > 100 * 1024 ? 'Compress to under 100KB' : null,
        ].filter(Boolean)
      }
    });

  } catch (error) {
    console.error('Get image info error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get image info',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
