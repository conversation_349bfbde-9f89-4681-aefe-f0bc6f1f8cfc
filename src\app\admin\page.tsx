'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/admin-layout';
import AdminHeader from '@/components/admin/admin-header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import {
  Users,
  FileText,
  FolderOpen,
  Plus,
  Activity,
  Eye,
  Settings,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  Shield,
  Database,
  Globe,
  UserCheck,
  Edit3,
  Calendar,
  Star,
  Zap
} from 'lucide-react';

interface AdminStats {
  overview: {
    totalUsers: number;
    totalPosts: number;
    publishedPosts: number;
    draftPosts: number;
    totalCategories: number;
    totalTags: number;
  };
  recentActivity: {
    newUsers: number;
    newPosts: number;
  };
  recentPosts: Array<{
    id: number;
    title: string;
    status: string;
    date: string;
    author: {
      id: number;
      username: string;
      displayName: string;
      email: string;
    } | null;
  }>;
  topAuthors: <AUTHORS>
    id: number;
    name: string;
    email: string;
    postCount: number;
  }>;
  userRoles: Array<{
    role: string;
    count: number;
  }>;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center min-h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <AdminHeader
        title="Admin Dashboard"
        description="Manage your blog content, users, and site settings from this central hub."
        actions={[
          {
            label: 'New Post',
            href: '/dashboard/new-post',
            icon: Plus,
            variant: 'default'
          },
          {
            label: 'View Site',
            href: '/',
            icon: Globe,
            variant: 'outline'
          },
          {
            label: 'Settings',
            href: '/admin/settings',
            icon: Settings,
            variant: 'outline'
          }
        ]}
        stats={stats ? [
          {
            label: 'Total Users',
            value: stats.overview.totalUsers,
            change: `+${stats.recentActivity.newUsers} new this month`,
            trend: stats.recentActivity.newUsers > 0 ? 'up' : 'neutral'
          },
          {
            label: 'Published Posts',
            value: stats.overview.publishedPosts,
            change: `${stats.overview.draftPosts} drafts`,
            trend: 'up'
          },
          {
            label: 'Total Posts',
            value: stats.overview.totalPosts,
            change: `+${stats.recentActivity.newPosts} this month`,
            trend: stats.recentActivity.newPosts > 0 ? 'up' : 'neutral'
          },
          {
            label: 'Categories',
            value: stats.overview.totalCategories,
            change: `${stats.overview.totalTags} tags`,
            trend: 'neutral'
          }
        ] : []}
      />

      <div className="space-y-8">
        {/* Quick Actions Grid */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/dashboard/new-post">
              <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-blue-200 dark:hover:border-blue-700">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                      <Plus className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">New Post</div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">Create content</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/admin/users">
              <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-green-200 dark:hover:border-green-700">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-800/50 transition-colors">
                      <Users className="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">Manage Users</div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">User accounts</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/admin/posts">
              <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-purple-200 dark:hover:border-purple-700">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-800/50 transition-colors">
                      <FileText className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">Manage Posts</div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">Content management</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/admin/categories">
              <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-orange-200 dark:hover:border-orange-700">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/50 rounded-xl flex items-center justify-center group-hover:bg-orange-200 dark:group-hover:bg-orange-800/50 transition-colors">
                      <FolderOpen className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">Categories</div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">Organize content</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>

        {/* Enhanced Stats Overview */}
        {stats && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Site Analytics</h2>
              <div className="flex space-x-2">
                <Link href="/api/verify-data">
                  <Button variant="outline" size="sm">
                    <Database className="w-4 h-4 mr-2" />
                    Data Health
                  </Button>
                </Link>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Users</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.overview.totalUsers}</p>
                      <div className="flex items-center mt-2">
                        <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          +{stats.recentActivity.newUsers} this month
                        </span>
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Published Posts</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.overview.publishedPosts}</p>
                      <div className="flex items-center mt-2">
                        <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          +{stats.recentActivity.newPosts} this month
                        </span>
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                      <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Draft Posts</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.overview.draftPosts}</p>
                      <div className="flex items-center mt-2">
                        <Edit3 className="w-4 h-4 text-orange-500 mr-1" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          Pending review
                        </span>
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                      <Edit3 className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20">
                <CardContent className="p-6 bg-white rounded-xl shadow-sm dark:bg-gray-900/80">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Categories</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.overview.totalCategories}</p>
                      <div className="flex items-center mt-2">
                        <Clock className="w-4 h-4 text-gray-500 mr-1" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {stats.overview.totalTags} tags
                        </span>
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-indigo-500/20 rounded-xl flex items-center justify-center">
                      <FolderOpen className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Recent Activity and Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Posts */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Recent Posts</span>
                </CardTitle>
                <Link href="/admin/posts">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {stats?.recentPosts && stats.recentPosts.length > 0 ? (
                <div className="space-y-4">
                  {stats.recentPosts.slice(0, 5).map((post) => (
                    <div key={post.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 truncate">{post.title}</h4>
                        <p className="text-sm text-gray-500">
                          by {post.author?.displayName || 'Unknown Author'} • {new Date(post.date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          post.status === 'publish' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {post.status}
                        </span>
                        <Link href={`/admin/posts/${post.id}`}>
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No recent posts</p>
              )}
            </CardContent>
          </Card>

          {/* Top Authors */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Star className="w-5 h-5" />
                  <span>Top Authors</span>
                </CardTitle>
                <Link href="/admin/users">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {stats?.topAuthors && stats.topAuthors.length > 0 ? (
                <div className="space-y-4">
                  {stats.topAuthors.slice(0, 5).map((author, index) => (
                    <div key={author.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                          {author.name.charAt(0).toUpperCase()}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 truncate">{author.name}</h4>
                          <p className="text-sm text-gray-500">{author.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                          {author.postCount} posts
                        </span>
                        <div className="text-sm font-medium text-gray-600">
                          #{index + 1}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No authors found</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Management Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FolderOpen className="w-5 h-5 text-blue-600" />
                <span>Content Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/posts" className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">All Posts</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Manage blog posts</div>
              </Link>
              <Link href="/admin/categories" className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">Categories</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Organize content categories</div>
              </Link>
              <Link href="/admin/tags" className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">Tags</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Manage content tags</div>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-green-600" />
                <span>User Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/users" className="block p-3 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">All Users</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Manage user accounts</div>
              </Link>
              <Link href="/admin/users/pending" className="block p-3 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">Pending Users</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Review new registrations</div>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5 text-purple-600" />
                <span>System Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/settings" className="block p-3 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">Site Configuration</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">General settings</div>
              </Link>
              <Link href="/api/verify-data" className="block p-3 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors bg-white shadow-sm dark:bg-gray-900/80">
                <div className="font-medium text-gray-900 dark:text-white">Data Health</div>
                <div className="text-sm text-gray-700 dark:text-gray-300">Database integrity check</div>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
