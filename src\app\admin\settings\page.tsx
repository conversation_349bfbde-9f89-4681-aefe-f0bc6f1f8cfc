'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import ImageUpload from '@/components/forms/image-upload';
import { 
  Settings, 
  Globe, 
  Users, 
  Image as ImageIcon, 
  Mail, 
  Shield, 
  Palette,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Upload
} from 'lucide-react';

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  timezone: string;
  language: string;
  logo: string;
  favicon: string;
  allowRegistration: boolean;
  requireEmailVerification: boolean;
  defaultUserRole: string;
  maintenanceMode: boolean;
  analyticsCode: string;
  socialLinks: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: string;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
}

export default function AdminSettings() {
  const [settings, setSettings] = useState<SiteSettings>({
    siteName: 'Wikify',
    siteDescription: 'How to Guide you can Trust',
    siteUrl: 'http://localhost:3000',
    adminEmail: '<EMAIL>',
    timezone: 'UTC',
    language: 'en',
    logo: '',
    favicon: '',
    allowRegistration: true,
    requireEmailVerification: false,
    defaultUserRole: 'AUTHOR',
    maintenanceMode: false,
    analyticsCode: '',
    socialLinks: {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: ''
    },
    emailSettings: {
      smtpHost: '',
      smtpPort: '587',
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '',
      fromName: ''
    }
  });

  const [loading, setLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const [activeTab, setActiveTab] = useState('general');
  const [healthData, setHealthData] = useState<any>(null);
  const [healthLoading, setHealthLoading] = useState(false);
  const [debugMode, setDebugMode] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();
        console.log('Loaded settings:', data);
        setSettings(prev => ({ ...prev, ...data }));
      } else {
        console.error('Failed to load settings:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setSaveStatus('saving');

    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      if (response.ok) {
        setSaveStatus('success');
        // Update settings with the response data to ensure consistency
        if (result.settings) {
          setSettings(result.settings);
        }
        setTimeout(() => setSaveStatus('idle'), 3000);
      } else {
        console.error('Save failed:', result.error);
        setSaveStatus('error');
        // Don't reload settings on error to preserve user changes
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSaveStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof SiteSettings] as any,
        [field]: value
      }
    }));
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
      setSettings({
        siteName: 'Wikify',
        siteDescription: 'How to Guide you can Trust',
        siteUrl: 'http://localhost:3000',
        adminEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        logo: '',
        favicon: '',
        allowRegistration: true,
        requireEmailVerification: false,
        defaultUserRole: 'AUTHOR',
        maintenanceMode: false,
        analyticsCode: '',
        socialLinks: {
          facebook: '',
          twitter: '',
          instagram: '',
          linkedin: ''
        },
        emailSettings: {
          smtpHost: '',
          smtpPort: '587',
          smtpUser: '',
          smtpPassword: '',
          fromEmail: '',
          fromName: ''
        }
      });
      setSaveStatus('idle');
    }
  };

  const handleExport = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `wikify-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          if (confirm('Are you sure you want to import these settings? This will overwrite your current settings.')) {
            setSettings(prev => ({ ...prev, ...importedSettings }));
            setSaveStatus('idle');
          }
        } catch (error) {
          alert('Invalid settings file. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
    // Reset the input
    event.target.value = '';
  };

  const handleBackup = async () => {
    try {
      const response = await fetch('/api/admin/backup');
      if (response.ok) {
        const backup = await response.json();
        const dataStr = JSON.stringify(backup.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `wikify-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
      } else {
        alert('Failed to create backup');
      }
    } catch (error) {
      console.error('Backup error:', error);
      alert('Failed to create backup');
    }
  };

  const handleRestore = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const backupData = JSON.parse(e.target?.result as string);
          if (confirm('Are you sure you want to restore from this backup? This will overwrite all current settings.')) {
            const response = await fetch('/api/admin/backup', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(backupData),
            });

            if (response.ok) {
              alert('Backup restored successfully! Please refresh the page.');
              window.location.reload();
            } else {
              alert('Failed to restore backup');
            }
          }
        } catch (error) {
          alert('Invalid backup file. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
    // Reset the input
    event.target.value = '';
  };

  const runHealthCheck = async () => {
    setHealthLoading(true);
    try {
      const response = await fetch('/api/admin/health');
      if (response.ok) {
        const result = await response.json();
        setHealthData(result.data);
      } else {
        console.error('Health check failed');
      }
    } catch (error) {
      console.error('Health check error:', error);
    } finally {
      setHealthLoading(false);
    }
  };

  const debugSaveToFile = async () => {
    try {
      const response = await fetch('/api/debug/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();
      if (result.success) {
        alert('Settings saved to file successfully!');
      } else {
        alert('Failed to save to file: ' + result.error);
      }
    } catch (error) {
      alert('Debug save failed: ' + error);
    }
  };

  const debugLoadFromFile = async () => {
    try {
      const response = await fetch('/api/debug/settings');
      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
        alert('Settings loaded from file successfully!');
      } else {
        alert('Failed to load from file: ' + result.error);
      }
    } catch (error) {
      alert('Debug load failed: ' + error);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'registration', label: 'Registration', icon: Users },
    { id: 'branding', label: 'Branding', icon: Palette },
    { id: 'email', label: 'Email', icon: Mail },
    { id: 'social', label: 'Social Media', icon: Globe },
    { id: 'advanced', label: 'Advanced', icon: Shield },
    { id: 'system', label: 'System Info', icon: AlertCircle }
  ];

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Site Settings</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Configure your site settings, registration options, and branding.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Import/Export/Reset/Backup Buttons */}
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  className="hidden"
                  id="import-settings"
                />
                <input
                  type="file"
                  accept=".json"
                  onChange={handleRestore}
                  className="hidden"
                  id="restore-backup"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-settings')?.click()}
                  className="text-sm"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
                <Button
                  variant="outline"
                  onClick={handleExport}
                  className="text-sm"
                >
                  <Upload className="w-4 h-4 mr-2 rotate-180" />
                  Export
                </Button>
                <Button
                  variant="outline"
                  onClick={handleBackup}
                  className="text-sm text-green-600 hover:text-green-700"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Backup
                </Button>
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('restore-backup')?.click()}
                  className="text-sm text-orange-600 hover:text-orange-700"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Restore
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="text-sm text-red-600 hover:text-red-700"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset
                </Button>

                {/* Debug Buttons */}
                <Button
                  variant="outline"
                  onClick={() => setDebugMode(!debugMode)}
                  className="text-sm text-purple-600 hover:text-purple-700"
                >
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Debug
                </Button>

                {debugMode && (
                  <>
                    <Button
                      variant="outline"
                      onClick={debugSaveToFile}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Save to File
                    </Button>
                    <Button
                      variant="outline"
                      onClick={debugLoadFromFile}
                      className="text-sm text-green-600 hover:text-green-700"
                    >
                      Load from File
                    </Button>
                  </>
                )}
              </div>

              <Button
                onClick={handleSave}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {loading ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </div>

          {/* Save Status */}
          {saveStatus !== 'idle' && (
            <div className={`mt-4 p-4 rounded-lg flex items-center space-x-2 ${
              saveStatus === 'success' ? 'bg-green-50 text-green-700' :
              saveStatus === 'error' ? 'bg-red-50 text-red-700' :
              'bg-blue-50 text-blue-700'
            }`}>
              {saveStatus === 'success' && <CheckCircle className="w-5 h-5" />}
              {saveStatus === 'error' && <AlertCircle className="w-5 h-5" />}
              {saveStatus === 'saving' && <RefreshCw className="w-5 h-5 animate-spin" />}
              <span>
                {saveStatus === 'success' && 'Settings saved successfully!'}
                {saveStatus === 'error' && 'Failed to save settings. Please try again.'}
                {saveStatus === 'saving' && 'Saving settings...'}
              </span>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="w-5 h-5" />
                    <span>Basic Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={settings.siteName}
                      onChange={(e) => handleInputChange('siteName', e.target.value)}
                      placeholder="Enter site name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="siteDescription">Site Description</Label>
                    <Textarea
                      id="siteDescription"
                      value={settings.siteDescription}
                      onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                      placeholder="Enter site description"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="siteUrl">Site URL</Label>
                    <Input
                      id="siteUrl"
                      value={settings.siteUrl}
                      onChange={(e) => handleInputChange('siteUrl', e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                  <div>
                    <Label htmlFor="adminEmail">Admin Email</Label>
                    <Input
                      id="adminEmail"
                      type="email"
                      value={settings.adminEmail}
                      onChange={(e) => handleInputChange('adminEmail', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Globe className="w-5 h-5" />
                    <span>Localization</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="timezone">Timezone</Label>
                    <select
                      id="timezone"
                      value={settings.timezone}
                      onChange={(e) => handleInputChange('timezone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="Asia/Dhaka">Bangladesh Time</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="language">Language</Label>
                    <select
                      id="language"
                      value={settings.language}
                      onChange={(e) => handleInputChange('language', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="en">English</option>
                      <option value="bn">Bengali</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="maintenanceMode"
                      checked={settings.maintenanceMode}
                      onCheckedChange={(checked) => handleInputChange('maintenanceMode', checked)}
                    />
                    <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Registration Settings */}
          {activeTab === 'registration' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>User Registration Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowRegistration">Allow User Registration</Label>
                    <p className="text-sm text-gray-500 mt-1">Allow new users to register on your site</p>
                  </div>
                  <Switch
                    id="allowRegistration"
                    checked={settings.allowRegistration}
                    onCheckedChange={(checked) => handleInputChange('allowRegistration', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
                    <p className="text-sm text-gray-500 mt-1">Users must verify their email before accessing the site</p>
                  </div>
                  <Switch
                    id="requireEmailVerification"
                    checked={settings.requireEmailVerification}
                    onCheckedChange={(checked) => handleInputChange('requireEmailVerification', checked)}
                  />
                </div>

                <div>
                  <Label htmlFor="defaultUserRole">Default User Role</Label>
                  <p className="text-sm text-gray-500 mt-1">Role assigned to new users upon registration</p>
                  <select
                    id="defaultUserRole"
                    value={settings.defaultUserRole}
                    onChange={(e) => handleInputChange('defaultUserRole', e.target.value)}
                    className="mt-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="AUTHOR">Author</option>
                    <option value="EDITOR">Editor</option>
                    <option value="SUBSCRIBER">Subscriber</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Branding Settings */}
          {activeTab === 'branding' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ImageIcon className="w-5 h-5" />
                    <span>Site Logo</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Label>Upload Logo</Label>
                    <ImageUpload
                      currentImage={settings.logo}
                      onUpload={(imageData) => handleInputChange('logo', imageData.url)}
                      onError={(error) => console.error('Logo upload error:', error)}
                      folder="site-branding"
                    />
                    <p className="text-sm text-gray-500">
                      Recommended size: 200x60px. Supports PNG, JPG, SVG formats.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ImageIcon className="w-5 h-5" />
                    <span>Favicon</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Label>Upload Favicon</Label>
                    <ImageUpload
                      currentImage={settings.favicon}
                      onUpload={(imageData) => handleInputChange('favicon', imageData.url)}
                      onError={(error) => console.error('Favicon upload error:', error)}
                      folder="site-branding"
                    />
                    <p className="text-sm text-gray-500">
                      Recommended size: 32x32px or 16x16px. ICO or PNG format.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Email Settings */}
          {activeTab === 'email' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="w-5 h-5" />
                  <span>Email Configuration</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpHost">SMTP Host</Label>
                    <Input
                      id="smtpHost"
                      value={settings.emailSettings.smtpHost}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'smtpHost', e.target.value)}
                      placeholder="smtp.gmail.com"
                    />
                  </div>
                  <div>
                    <Label htmlFor="smtpPort">SMTP Port</Label>
                    <Input
                      id="smtpPort"
                      value={settings.emailSettings.smtpPort}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'smtpPort', e.target.value)}
                      placeholder="587"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpUser">SMTP Username</Label>
                    <Input
                      id="smtpUser"
                      value={settings.emailSettings.smtpUser}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'smtpUser', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="smtpPassword">SMTP Password</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={settings.emailSettings.smtpPassword}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'smtpPassword', e.target.value)}
                      placeholder="Your app password"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fromEmail">From Email</Label>
                    <Input
                      id="fromEmail"
                      type="email"
                      value={settings.emailSettings.fromEmail}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'fromEmail', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="fromName">From Name</Label>
                    <Input
                      id="fromName"
                      value={settings.emailSettings.fromName}
                      onChange={(e) => handleNestedInputChange('emailSettings', 'fromName', e.target.value)}
                      placeholder="Your Site Name"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Social Media Settings */}
          {activeTab === 'social' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Globe className="w-5 h-5" />
                  <span>Social Media Links</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="facebook">Facebook URL</Label>
                  <Input
                    id="facebook"
                    value={settings.socialLinks.facebook}
                    onChange={(e) => handleNestedInputChange('socialLinks', 'facebook', e.target.value)}
                    placeholder="https://facebook.com/yourpage"
                  />
                </div>
                <div>
                  <Label htmlFor="twitter">Twitter URL</Label>
                  <Input
                    id="twitter"
                    value={settings.socialLinks.twitter}
                    onChange={(e) => handleNestedInputChange('socialLinks', 'twitter', e.target.value)}
                    placeholder="https://twitter.com/youraccount"
                  />
                </div>
                <div>
                  <Label htmlFor="instagram">Instagram URL</Label>
                  <Input
                    id="instagram"
                    value={settings.socialLinks.instagram}
                    onChange={(e) => handleNestedInputChange('socialLinks', 'instagram', e.target.value)}
                    placeholder="https://instagram.com/youraccount"
                  />
                </div>
                <div>
                  <Label htmlFor="linkedin">LinkedIn URL</Label>
                  <Input
                    id="linkedin"
                    value={settings.socialLinks.linkedin}
                    onChange={(e) => handleNestedInputChange('socialLinks', 'linkedin', e.target.value)}
                    placeholder="https://linkedin.com/company/yourcompany"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Advanced Settings */}
          {activeTab === 'advanced' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>Advanced Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="analyticsCode">Google Analytics Code</Label>
                  <Textarea
                    id="analyticsCode"
                    value={settings.analyticsCode}
                    onChange={(e) => handleInputChange('analyticsCode', e.target.value)}
                    placeholder="<!-- Google Analytics code here -->"
                    rows={4}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Paste your Google Analytics tracking code here
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* System Info */}
          {activeTab === 'system' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-5 h-5" />
                      <span>System Health Check</span>
                    </div>
                    <Button
                      onClick={runHealthCheck}
                      disabled={healthLoading}
                      variant="outline"
                      size="sm"
                    >
                      {healthLoading ? (
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4 mr-2" />
                      )}
                      Run Check
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {healthData ? (
                    <div className="space-y-4">
                      <div className={`p-4 rounded-lg flex items-center space-x-2 ${
                        healthData.overall === 'healthy' ? 'bg-green-50 text-green-700' :
                        healthData.overall === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-red-50 text-red-700'
                      }`}>
                        {healthData.overall === 'healthy' && <CheckCircle className="w-5 h-5" />}
                        {healthData.overall === 'warning' && <AlertCircle className="w-5 h-5" />}
                        {healthData.overall === 'error' && <AlertCircle className="w-5 h-5" />}
                        <span className="font-medium">
                          Overall Status: {healthData.overall.charAt(0).toUpperCase() + healthData.overall.slice(1)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {healthData.checks.map((check: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">{check.name}</h4>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                check.status === 'healthy' ? 'bg-green-100 text-green-700' :
                                check.status === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-red-100 text-red-700'
                              }`}>
                                {check.status}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600">{check.message}</p>
                          </div>
                        ))}
                      </div>

                      <p className="text-sm text-gray-500">
                        Last checked: {new Date(healthData.timestamp).toLocaleString()}
                      </p>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Click "Run Check" to perform a system health check</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
