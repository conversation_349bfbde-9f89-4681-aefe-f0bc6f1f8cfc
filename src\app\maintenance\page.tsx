'use client';

import { useState, useEffect } from 'react';
import { Settings, Clock, Mail, RefreshCw } from 'lucide-react';

export default function MaintenancePage() {
  const [timeLeft, setTimeLeft] = useState('');

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const nextHour = new Date(now);
      nextHour.setHours(now.getHours() + 1, 0, 0, 0);
      
      const diff = nextHour.getTime() - now.getTime();
      const minutes = Math.floor(diff / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Logo/Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
            <Settings className="w-12 h-12 text-white animate-spin" style={{ animationDuration: '3s' }} />
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            We'll Be Right Back!
          </h1>
          
          <p className="text-xl text-gray-200 mb-8 leading-relaxed">
            Our website is currently undergoing scheduled maintenance to improve your experience. 
            We apologize for any inconvenience and appreciate your patience.
          </p>

          {/* Estimated Time */}
          <div className="bg-white/10 rounded-lg p-6 mb-8 border border-white/20">
            <div className="flex items-center justify-center space-x-3 mb-3">
              <Clock className="w-6 h-6 text-blue-300" />
              <h3 className="text-lg font-semibold text-white">Estimated Time Remaining</h3>
            </div>
            <div className="text-3xl font-mono font-bold text-blue-300">
              {timeLeft}
            </div>
            <p className="text-sm text-gray-300 mt-2">
              We're working hard to get everything back online as soon as possible.
            </p>
          </div>

          {/* What We're Doing */}
          <div className="text-left mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 text-center">What We're Working On:</h3>
            <ul className="space-y-2 text-gray-200">
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Database optimization and performance improvements</span>
              </li>
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Security updates and system patches</span>
              </li>
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>New features and user experience enhancements</span>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="border-t border-white/20 pt-6">
            <p className="text-gray-300 mb-4">
              Need immediate assistance? Contact our support team:
            </p>
            <div className="flex items-center justify-center space-x-2 text-blue-300">
              <Mail className="w-5 h-5" />
              <a href="mailto:<EMAIL>" className="hover:text-blue-200 transition-colors">
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Refresh Button */}
          <div className="mt-8">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto"
            >
              <RefreshCw className="w-5 h-5" />
              <span>Check Again</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-gray-400 text-sm">
            Thank you for your patience and continued support.
          </p>
          <p className="text-gray-500 text-xs mt-2">
            © 2024 Wikify Blog. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
