import { db } from '@/lib/db';
import { options } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  timezone: string;
  language: string;
  logo: string;
  favicon: string;
  allowRegistration: boolean;
  requireEmailVerification: boolean;
  defaultUserRole: string;
  maintenanceMode: boolean;
  analyticsCode: string;
  socialLinks: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: string;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
}

const defaultSettings: SiteSettings = {
  siteName: 'Wikify',
  siteDescription: 'How to Guide you can Trust',
  siteUrl: 'http://localhost:3000',
  adminEmail: '<EMAIL>',
  timezone: 'UTC',
  language: 'en',
  logo: '',
  favicon: '',
  allowRegistration: true,
  requireEmailVerification: false,
  defaultUserRole: 'AUTHOR',
  maintenanceMode: false,
  analyticsCode: '',
  socialLinks: {
    facebook: '',
    twitter: '',
    instagram: '',
    linkedin: ''
  },
  emailSettings: {
    smtpHost: '',
    smtpPort: '587',
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: ''
  }
};

// Cache for settings to avoid repeated database calls
let settingsCache: SiteSettings | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function getSiteSettings(): Promise<SiteSettings> {
  // Check if cache is still valid
  if (settingsCache && Date.now() - cacheTimestamp < CACHE_DURATION) {
    return settingsCache;
  }

  try {
    const settingsData = await db
      .select()
      .from(options)
      .where(eq(options.option_name, 'site_settings'));

    if (settingsData.length > 0) {
      const savedSettings = JSON.parse(settingsData[0].option_value);
      settingsCache = { ...defaultSettings, ...savedSettings };
    } else {
      settingsCache = defaultSettings;
    }

    cacheTimestamp = Date.now();
    return settingsCache;
  } catch (error) {
    console.error('Failed to load site settings:', error);
    return defaultSettings;
  }
}

export async function updateSiteSettings(newSettings: Partial<SiteSettings>): Promise<boolean> {
  try {
    const currentSettings = await getSiteSettings();
    const updatedSettings = { ...currentSettings, ...newSettings };
    
    const settingsJson = JSON.stringify(updatedSettings);
    
    // Check if settings already exist
    const existingSettings = await db
      .select()
      .from(options)
      .where(eq(options.option_name, 'site_settings'));

    if (existingSettings.length > 0) {
      // Update existing settings
      await db
        .update(options)
        .set({ 
          option_value: settingsJson,
          autoload: 'yes'
        })
        .where(eq(options.option_name, 'site_settings'));
    } else {
      // Insert new settings
      await db
        .insert(options)
        .values({
          option_name: 'site_settings',
          option_value: settingsJson,
          autoload: 'yes'
        });
    }

    // Update cache
    settingsCache = updatedSettings;
    cacheTimestamp = Date.now();
    
    return true;
  } catch (error) {
    console.error('Failed to update site settings:', error);
    return false;
  }
}

export function clearSettingsCache(): void {
  settingsCache = null;
  cacheTimestamp = 0;
}

// Helper functions for specific settings
export async function isMaintenanceMode(): Promise<boolean> {
  const settings = await getSiteSettings();
  return settings.maintenanceMode;
}

export async function getSiteName(): Promise<string> {
  const settings = await getSiteSettings();
  return settings.siteName;
}

export async function getSiteDescription(): Promise<string> {
  const settings = await getSiteSettings();
  return settings.siteDescription;
}

export async function isRegistrationAllowed(): Promise<boolean> {
  const settings = await getSiteSettings();
  return settings.allowRegistration;
}
