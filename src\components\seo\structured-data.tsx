'use client';

interface Author {
  name: string;
  url?: string;
  image?: string;
}

interface Article {
  title: string;
  description: string;
  url: string;
  image?: string;
  author: Author;
  publishedTime: string;
  modifiedTime: string;
  category?: string;
  tags?: string[];
}

interface Organization {
  name: string;
  url: string;
  logo?: string;
  description: string;
  socialLinks?: string[];
}

interface WebSite {
  name: string;
  url: string;
  description: string;
  searchUrl?: string;
}

interface StructuredDataProps {
  type: 'article' | 'organization' | 'website' | 'breadcrumb' | 'author';
  data: any;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const generateSchema = () => {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz';
    
    switch (type) {
      case 'article':
        const article: Article = data;
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": article.title,
          "description": article.description,
          "url": article.url,
          "image": article.image ? {
            "@type": "ImageObject",
            "url": article.image,
            "width": 1200,
            "height": 630
          } : undefined,
          "author": {
            "@type": "Person",
            "name": article.author.name,
            "url": article.author.url,
            "image": article.author.image
          },
          "publisher": {
            "@type": "Organization",
            "name": "Wikify",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`,
              "width": 200,
              "height": 60
            }
          },
          "datePublished": article.publishedTime,
          "dateModified": article.modifiedTime,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": article.url
          },
          "articleSection": article.category,
          "keywords": article.tags?.join(', ')
        };

      case 'organization':
        const org: Organization = data;
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": org.name,
          "url": org.url,
          "description": org.description,
          "logo": org.logo ? {
            "@type": "ImageObject",
            "url": org.logo
          } : undefined,
          "sameAs": org.socialLinks || []
        };

      case 'website':
        const website: WebSite = data;
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": website.name,
          "url": website.url,
          "description": website.description,
          "potentialAction": website.searchUrl ? {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": website.searchUrl
            },
            "query-input": "required name=search_term_string"
          } : undefined
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": item.url
          }))
        };

      case 'author':
        return {
          "@context": "https://schema.org",
          "@type": "Person",
          "name": data.name,
          "url": data.url,
          "image": data.image,
          "description": data.bio,
          "jobTitle": "Content Writer",
          "worksFor": {
            "@type": "Organization",
            "name": "Wikify",
            "url": baseUrl
          }
        };

      default:
        return null;
    }
  };

  const schema = generateSchema();
  
  if (!schema) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
