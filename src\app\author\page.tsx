'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/layout/layout';
import Link from 'next/link';

interface Author {
  id: number;
  username: string;
  displayName: string;
  nicename: string;
  bio: string;
  registered: string;
  role: string;
  stats: {
    posts: number;
  };
}

const AuthorsPage = () => {
  const [authors, setAuthors] = useState<Author[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAuthors, setTotalAuthors] = useState(0);
  const authorsPerPage = 12;

  useEffect(() => {
    fetchAuthors(1);
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== '') {
        fetchAuthors(1);
      } else {
        fetchAuthors(currentPage);
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const fetchAuthors = async (page: number = 1) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: authorsPerPage.toString(),
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/authors?${params}`);
      const data = await response.json();

      if (data.success) {
        setAuthors(data.data);
        setCurrentPage(page);
        setTotalPages(Math.ceil(data.total / authorsPerPage));
        setTotalAuthors(data.total);
      } else {
        setError(data.error || 'Failed to load authors');
      }
    } catch (error) {
      console.error('Error fetching authors:', error);
      setError('Failed to load authors');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
    fetchAuthors(1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'text-red-600 bg-red-100';
      case 'EDITOR': return 'text-blue-600 bg-blue-100';
      case 'AUTHOR': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getGradientForRole = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'from-red-400 to-pink-500';
      case 'EDITOR': return 'from-blue-400 to-indigo-500';
      case 'AUTHOR': return 'from-green-400 to-emerald-500';
      default: return 'from-gray-400 to-slate-500';
    }
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      fetchAuthors(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  if (loading && authors.length === 0) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
          {/* Loading Header */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 relative overflow-hidden">
            <div className="absolute inset-0 bg-black opacity-20"></div>
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
              <div className="animate-pulse">
                <div className="w-20 h-20 bg-white/20 rounded-full mx-auto mb-8"></div>
                <div className="h-12 bg-white/20 rounded-lg mb-6 max-w-md mx-auto"></div>
                <div className="h-6 bg-white/20 rounded-lg max-w-2xl mx-auto"></div>
              </div>
            </div>
          </div>

          {/* Loading Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-3xl shadow-xl overflow-hidden animate-pulse">
                  <div className="h-24 bg-gray-200"></div>
                  <div className="p-8">
                    <div className="w-28 h-28 bg-gray-200 rounded-full mx-auto -mt-16 mb-6"></div>
                    <div className="h-6 bg-gray-200 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Enhanced Hero Section */}
        <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 relative overflow-hidden">
          <div className="absolute inset-0 bg-black opacity-20"></div>
          
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-white opacity-10 rounded-full animate-pulse"></div>
            <div className="absolute top-32 -left-20 w-48 h-48 bg-white opacity-10 rounded-full animate-pulse delay-1000"></div>
            <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-white opacity-10 rounded-full animate-pulse delay-2000"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full mb-8 border border-white/30">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h1 className="text-5xl font-bold text-white mb-6">
                Meet Our Authors
              </h1>
              <p className="text-xl text-indigo-100 max-w-3xl mx-auto leading-relaxed">
                Discover the brilliant minds behind our content. Our diverse team of writers, developers, and thought leaders share their expertise to help you grow.
              </p>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#grid)" />
            </svg>
          </div>
        </div>

        {/* Enhanced Search and Filter Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100 -mt-16 relative z-10">
            <div className="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0">
              <div className="flex-1 max-w-2xl">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search authors by name..."
                    value={searchTerm}
                    onChange={handleSearch}
                    className="w-full pl-12 pr-12 py-4 border border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg"
                  />
                  {searchTerm && (
                    <button
                      onClick={clearSearch}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-6 text-gray-600">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span className="font-semibold text-lg">{totalAuthors}</span>
                  <span>Authors</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Authors Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {error ? (
            <div className="text-center py-16">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h3>
              <p className="text-gray-600 mb-8">{error}</p>
              <button
                onClick={() => fetchAuthors(1)}
                className="bg-indigo-600 text-white px-8 py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold"
              >
                Try Again
              </button>
            </div>
          ) : authors.length === 0 && !loading ? (
            <div className="text-center py-16">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-6">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">No authors found</h3>
              <p className="text-gray-600 mb-8">
                {searchTerm ? `No authors match "${searchTerm}". Try a different search term.` : 'No authors available at the moment.'}
              </p>
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="bg-indigo-600 text-white px-8 py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold"
                >
                  Clear Search
                </button>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
                {authors.map((author) => (
                <Link
                  key={author.id}
                  href={`/author/${author.nicename}`}
                  className="group block"
                >
                  <div className="bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2 hover:scale-105">
                    {/* Card Header with Gradient */}
                    <div className={`h-24 bg-gradient-to-r ${getGradientForRole(author.role)} relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-black opacity-10"></div>
                      {/* Decorative circles */}
                      <div className="absolute -top-4 -right-4 w-16 h-16 bg-white opacity-20 rounded-full"></div>
                      <div className="absolute top-8 -left-4 w-12 h-12 bg-white opacity-20 rounded-full"></div>
                    </div>

                    <div className="p-8 relative">
                      {/* Avatar */}
                      <div className="flex justify-center -mt-16 mb-6">
                        <div className="relative">
                          <div className={`w-28 h-28 rounded-full bg-gradient-to-br ${getGradientForRole(author.role)} p-1 shadow-2xl`}>
                            <div className="w-full h-full rounded-full bg-white flex items-center justify-center text-2xl font-bold text-gray-700 shadow-inner">
                              {author.displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </div>
                          </div>
                          {/* Online indicator */}
                          <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-400 rounded-full border-4 border-white shadow-lg"></div>
                        </div>
                      </div>

                      {/* Author Info */}
                      <div className="text-center space-y-4">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">
                            {author.displayName}
                          </h3>
                          <p className="text-gray-500 text-sm">@{author.username}</p>
                        </div>



                        {/* Bio */}
                        {author.bio && (
                          <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                            {author.bio}
                          </p>
                        )}

                        {/* Stats */}
                        <div className="flex items-center justify-center space-x-6 pt-4 border-t border-gray-100">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-600">{author.stats.posts}</div>
                            <div className="text-xs text-gray-500 uppercase tracking-wide">Posts</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {new Date(author.registered).getFullYear()}
                            </div>
                            <div className="text-xs text-gray-500 uppercase tracking-wide">Joined</div>
                          </div>
                        </div>

                        {/* View Profile Button */}
                        <div className="pt-4">
                          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-6 py-2 rounded-xl text-sm font-semibold group-hover:from-indigo-600 group-hover:to-purple-700 transition-all duration-300 transform group-hover:scale-105">
                            View Profile
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
                ))}
              </div>

              {/* Enhanced Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-4 py-2 rounded-xl border border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {[...Array(totalPages)].map((_, index) => {
                    const page = index + 1;
                    const isCurrentPage = page === currentPage;

                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 2 && page <= currentPage + 2)
                    ) {
                      return (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-4 py-2 rounded-xl font-semibold transition-all duration-300 ${
                            isCurrentPage
                              ? 'bg-indigo-600 text-white shadow-lg transform scale-110'
                              : 'border border-gray-200 text-gray-600 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    } else if (
                      page === currentPage - 3 ||
                      page === currentPage + 3
                    ) {
                      return (
                        <span key={page} className="px-2 py-2 text-gray-400">
                          ...
                        </span>
                      );
                    }
                    return null;
                  })}

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 rounded-xl border border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default AuthorsPage;
