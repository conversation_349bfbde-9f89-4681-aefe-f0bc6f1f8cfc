import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, usermeta, USER_STATUS } from '@/lib/db/schema';
import { eq, desc, like, or, and, count, sql, ne } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { getUsersAdsPermissions } from '@/lib/utils/ads-permissions';

// GET /api/users - List users with pagination and search (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const includeAdsPermissions = searchParams.get('includeAdsPermissions') === 'true';
    
    const offset = (page - 1) * limit;

    // Build where conditions for users table
    let userWhereConditions = [];

    // Exclude pending users (status 0) from main user management page
    userWhereConditions.push(ne(users.user_status, USER_STATUS.PENDING));

    if (search) {
      userWhereConditions.push(
        or(
          like(users.user_login, `%${search}%`),
          like(users.user_email, `%${search}%`),
          like(users.display_name, `%${search}%`)
        )
      );
    }

    // Get users with their roles - use a more specific join to avoid duplicates
    let query = db
      .select({
        user: users,
        capabilities: usermeta
      })
      .from(users)
      .leftJoin(usermeta, and(
        eq(users.ID, usermeta.user_id),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      ))
      .orderBy(desc(users.user_registered))
      .limit(limit)
      .offset(offset);

    // Apply user search conditions
    query = query.where(and(...userWhereConditions));

    const result = await query;

    // Get total count for pagination with same filters
    let countQuery = db
      .select({ count: count() })
      .from(users)
      .where(and(...userWhereConditions));

    const [totalCount] = await countQuery;

    // Process users and extract roles
    const processedUsers = result.map(({ user, capabilities }) => {
      let userRole = 'AUTHOR';
      if (capabilities?.meta_value) {
        const caps = capabilities.meta_value;
        if (caps.includes('administrator')) userRole = 'ADMIN';
        else if (caps.includes('editor')) userRole = 'EDITOR';
        else if (caps.includes('author')) userRole = 'AUTHOR';
      }

      return {
        id: user.ID,
        username: user.user_login,
        email: user.user_email,
        displayName: user.display_name,
        nicename: user.user_nicename,
        url: user.user_url,
        registered: user.user_registered,
        status: user.user_status,
        role: userRole
      };
    });

    // Filter by role if specified
    const filteredUsers = role
      ? processedUsers.filter(user => user.role === role)
      : processedUsers;

    // Add ads permissions if requested
    let finalUsers = filteredUsers;
    if (includeAdsPermissions) {
      const userIds = filteredUsers.map(user => user.id);
      const adsPermissions = await getUsersAdsPermissions(userIds);

      finalUsers = filteredUsers.map(user => ({
        ...user,
        canInsertAds: adsPermissions[user.id] || false
      }));
    }

    return NextResponse.json({
      success: true,
      data: finalUsers,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/users - Create new user (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { username, email, password, displayName, role = 'AUTHOR', url = '' } = body;

    if (!username || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(or(
        eq(users.user_login, username),
        eq(users.user_email, email)
      ))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { success: false, error: 'User with this username or email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    const now = new Date();

    // Create user
    const result = await db.insert(users).values({
      user_login: username,
      user_pass: hashedPassword,
      user_nicename: username.toLowerCase().replace(/[^a-z0-9]/g, '-'),
      user_email: email,
      user_url: url,
      user_registered: now,
      user_activation_key: '',
      user_status: 0,
      display_name: displayName || username
    });

    const userId = result.insertId;

    // Set user capabilities based on role
    let capabilities = '';
    switch (role) {
      case 'ADMIN':
        capabilities = 'a:1:{s:13:"administrator";b:1;}';
        break;
      case 'EDITOR':
        capabilities = 'a:1:{s:6:"editor";b:1;}';
        break;
      case 'AUTHOR':
      default:
        capabilities = 'a:1:{s:6:"author";b:1;}';
        break;
    }

    // Insert user capabilities
    await db.insert(usermeta).values({
      user_id: userId,
      meta_key: 'wikify1h_capabilities',
      meta_value: capabilities
    });

    return NextResponse.json({
      success: true,
      data: {
        id: userId,
        username,
        email,
        displayName: displayName || username,
        role,
        registered: now
      }
    });

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
