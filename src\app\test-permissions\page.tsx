'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import Layout from '@/components/layout/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPermissionsPage() {
  const { data: session } = useSession();
  const [postId, setPostId] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testEditPermission = async () => {
    if (!postId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/posts/${postId}/edit`);
      const data = await response.json();
      setResult({
        type: 'edit',
        success: response.ok,
        status: response.status,
        data
      });
    } catch (error) {
      setResult({
        type: 'edit',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testDirectAccess = () => {
    if (!postId) return;
    window.open(`/dashboard/edit-post/${postId}`, '_blank');
  };

  if (!session?.user) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-8">
          <Card>
            <CardContent className="text-center py-8">
              <p>Please log in to test permissions</p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-8">
        <Card>
          <CardHeader>
            <CardTitle>Test Post Edit Permissions</CardTitle>
            <p className="text-gray-600">
              Current user: {session.user.name} ({session.user.role})
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Post ID to test:
                </label>
                <input
                  type="number"
                  value={postId}
                  onChange={(e) => setPostId(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="Enter post ID"
                />
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={testEditPermission}
                  disabled={!postId || loading}
                >
                  {loading ? 'Testing...' : 'Test API Permission'}
                </Button>
                
                <Button
                  onClick={testDirectAccess}
                  disabled={!postId}
                  variant="outline"
                >
                  Test Direct Page Access
                </Button>
              </div>

              {result && (
                <div className="mt-6">
                  <h3 className="font-medium mb-2">Result:</h3>
                  <div className={`p-4 rounded border ${
                    result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <p className="font-medium">
                      Status: {result.success ? 'Success' : 'Failed'} ({result.status})
                    </p>
                    <pre className="mt-2 text-sm overflow-auto">
                      {JSON.stringify(result.data || result.error, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              <div className="mt-8 p-4 bg-blue-50 rounded">
                <h4 className="font-medium mb-2">Permission Rules:</h4>
                <ul className="text-sm space-y-1">
                  <li>• Post owners can edit their own posts</li>
                  <li>• Admins can edit any post</li>
                  <li>• Editors can edit any post</li>
                  <li>• Other users cannot edit posts they don't own</li>
                  <li>• Only admins and post owners can delete posts</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
