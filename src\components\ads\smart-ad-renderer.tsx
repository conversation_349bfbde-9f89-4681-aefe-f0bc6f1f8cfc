'use client';

import { useMemo } from 'react';
import { parseAdCode, hasCorsIssues, generateFallbackAd } from '@/lib/utils/ad-parser';
import AdverticaAdRenderer from './advertica-ad-renderer';
import ManualAdverticaRenderer from './manual-advertica-renderer';

interface SmartAdRendererProps {
  adCode: string;
  className?: string;
}

/**
 * Smart Ad Renderer
 * Automatically detects ad type and uses appropriate renderer
 * Handles CORS issues gracefully
 */
export default function SmartAdRenderer({ adCode, className = '' }: SmartAdRendererProps) {
  const parsedAd = useMemo(() => parseAdCode(adCode), [adCode]);
  const hasCorsProblem = useMemo(() => hasCorsIssues(adCode), [adCode]);

  console.log('Smart Ad Renderer:', {
    type: parsedAd.type,
    hasCorsProblem,
    dataDomain: parsedAd.dataDomain,
    dataAffquery: parsedAd.dataAffquery
  });

  // Handle empty or invalid ad code
  if (!adCode || !adCode.trim()) {
    return (
      <div className={`smart-ad-container ${className}`}>
        <div style={{ 
          padding: '20px', 
          textAlign: 'center', 
          color: '#999',
          border: '1px dashed #ddd',
          borderRadius: '4px'
        }}>
          No ad code provided
        </div>
      </div>
    );
  }

  // Handle Advertica ads
  if (parsedAd.type === 'advertica') {
    if (hasCorsProblem) {
      console.log('Using Manual Advertica Renderer due to CORS issues');
      return (
        <div className={`smart-ad-container ${className}`}>
          <ManualAdverticaRenderer
            dataDomain={parsedAd.dataDomain!}
            dataAffquery={parsedAd.dataAffquery!}
            dataWidth={parsedAd.dataWidth}
            dataHeight={parsedAd.dataHeight}
            className={parsedAd.className}
          />
        </div>
      );
    } else {
      console.log('Using Standard Advertica Renderer');
      return (
        <div className={`smart-ad-container ${className}`}>
          <AdverticaAdRenderer adCode={adCode} />
        </div>
      );
    }
  }

  // Handle AdSense ads
  if (parsedAd.type === 'adsense') {
    console.log('Rendering AdSense ad');
    return (
      <div className={`smart-ad-container ${className}`}>
        <div dangerouslySetInnerHTML={{ __html: adCode }} />
      </div>
    );
  }

  // Handle generic ads
  console.log('Rendering generic ad');
  
  // Check if it might have CORS issues
  if (hasCorsProblem) {
    console.log('Generic ad has potential CORS issues, showing fallback');
    const fallbackHtml = generateFallbackAd(parsedAd);
    return (
      <div className={`smart-ad-container ${className}`}>
        <div dangerouslySetInnerHTML={{ __html: fallbackHtml }} />
        <div style={{ 
          fontSize: '10px', 
          color: '#999', 
          textAlign: 'center', 
          marginTop: '5px' 
        }}>
          Original ad blocked by CORS policy
        </div>
      </div>
    );
  }

  // Default rendering for generic ads
  return (
    <div className={`smart-ad-container ${className}`}>
      <div dangerouslySetInnerHTML={{ __html: adCode }} />
    </div>
  );
}
