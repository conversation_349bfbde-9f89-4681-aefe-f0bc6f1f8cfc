import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { posts, users, terms, term_taxonomy, term_relationships } from '@/lib/db/schema';
import { eq, desc, and, like, or, count } from 'drizzle-orm';
import { processWordPressContent, generateExcerpt, getFeaturedImage } from '@/lib/utils/content-processor';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const author = searchParams.get('author');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    const offset = (page - 1) * limit;

    // Base query for published posts only (exclude trash, draft, private, etc.)
    let baseQuery = db
      .selectDistinct({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'publish')
      ));

    // Add search conditions
    const conditions = [];

    // Text search in title and content
    if (query.trim()) {
      conditions.push(
        or(
          like(posts.post_title, `%${query}%`),
          like(posts.post_content, `%${query}%`),
          like(posts.post_excerpt, `%${query}%`)
        )
      );
    }

    // Author filter
    if (author) {
      conditions.push(eq(posts.post_author, parseInt(author)));
    }

    // Apply conditions
    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions));
    }

    // Category/Tag filtering requires subqueries
    let finalResults;
    
    if (category || tag) {
      // Get posts with specific category or tag
      const taxonomyConditions = [];
      
      if (category) {
        taxonomyConditions.push(
          and(
            eq(term_taxonomy.taxonomy, 'category'),
            eq(terms.slug, category)
          )
        );
      }
      
      if (tag) {
        taxonomyConditions.push(
          and(
            eq(term_taxonomy.taxonomy, 'post_tag'),
            eq(terms.slug, tag)
          )
        );
      }

      // Get post IDs that match taxonomy filters
      const taxonomyQuery = db
        .select({ postId: term_relationships.object_id })
        .from(term_relationships)
        .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
        .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
        .where(or(...taxonomyConditions));

      const taxonomyResults = await taxonomyQuery;
      const postIds = taxonomyResults.map(r => r.postId);

      if (postIds.length === 0) {
        return NextResponse.json({
          success: true,
          data: [],
          pagination: {
            page,
            limit,
            total: 0,
            hasMore: false
          }
        });
      }

      // Filter base query by post IDs
      baseQuery = baseQuery.where(
        and(
          ...conditions,
          or(...postIds.map(id => eq(posts.ID, id)))
        )
      );
    }

    // Execute query with pagination
    const results = await baseQuery
      .orderBy(desc(posts.post_date))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination - ensure we only count published posts
    const countQuery = db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'publish'),
        ...conditions
      ));

    const [{ count: totalCount }] = await countQuery;

    // Format results
    const formattedResults = await Promise.all(
      results.map(async ({ post, author }) => ({
        id: post.ID,
        title: post.post_title,
        slug: post.post_name,
        excerpt: post.post_excerpt || generateExcerpt(post.post_content),
        content: processWordPressContent(post.post_content),
        status: post.post_status,
        date: post.post_date,
        modified: post.post_modified,
        featured_image: await getFeaturedImage(post.ID, post.post_content),
        author: author ? {
          id: author.ID,
          username: author.user_login,
          displayName: author.display_name,
          email: author.user_email
        } : null,
        commentCount: post.comment_count
      }))
    );

    // Final deduplication by ID and ensure no trash posts
    const uniqueResults = formattedResults
      .filter(post => post.status === 'publish') // Extra safety: only published posts
      .filter((post, index, self) =>
        index === self.findIndex(p => p.id === post.id)
      );

    return NextResponse.json({
      success: true,
      data: uniqueResults,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: offset + limit < totalCount
      },
      query: {
        search: query,
        category,
        tag,
        author
      }
    });

  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Search failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
