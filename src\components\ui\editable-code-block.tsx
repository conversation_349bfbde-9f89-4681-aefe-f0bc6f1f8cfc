'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import CodeBlockModal from '@/components/forms/code-block-modal';

interface EditableCodeBlockProps {
  code: string;
  language?: string;
  onUpdate?: (newCode: string, newLanguage: string) => void;
  isEditable?: boolean;
  className?: string;
}

const EditableCodeBlock: React.FC<EditableCodeBlockProps> = ({
  code,
  language = 'javascript',
  onUpdate,
  isEditable = false,
  className = ''
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleUpdate = (newCode: string, newLanguage: string) => {
    if (onUpdate) {
      onUpdate(newCode, newLanguage);
    }
    setShowEditModal(false);
  };

  return (
    <div className={`relative group ${className}`}>
      {/* Code Block Header */}
      <div className="flex items-center justify-between bg-gray-800 text-gray-300 px-4 py-2 rounded-t-xl text-sm">
        <span className="font-medium">{language}</span>
        <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {/* Copy Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-6 px-2 text-gray-300 hover:text-white hover:bg-gray-700"
            title="Copy Code"
          >
            {copied ? (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            )}
          </Button>

          {/* Edit Button */}
          {isEditable && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowEditModal(true)}
              className="h-6 px-2 text-gray-300 hover:text-white hover:bg-gray-700"
              title="Edit Code"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
          )}
        </div>
      </div>

      {/* Code Content */}
      <pre className="bg-gray-900 text-gray-100 rounded-b-xl p-6 overflow-x-auto font-mono text-sm m-0">
        <code className={`language-${language}`}>{code}</code>
      </pre>

      {/* Edit Modal */}
      {isEditable && (
        <CodeBlockModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onInsert={handleUpdate}
          initialCode={code}
          initialLanguage={language}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default EditableCodeBlock;
