import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships } from '@/lib/db/schema';
import { eq, desc, like, count, sql } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils/slug';

// GET /api/tags - List tags with pagination and search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const includeEmpty = searchParams.get('include_empty') === 'true';
    
    const offset = (page - 1) * limit;

    // Build query for tags
    let query = db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .orderBy(desc(term_taxonomy.count), terms.name)
      .limit(limit)
      .offset(offset);

    if (search) {
      query = query.where(like(terms.name, `%${search}%`));
    }

    if (!includeEmpty) {
      query = query.where(sql`${term_taxonomy.count} > 0`);
    }

    const result = await query;

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'));

    // Process tags
    const tags = result.map(({ term, taxonomy }) => ({
      id: term?.term_id,
      name: term?.name,
      slug: term?.slug,
      description: taxonomy?.description || '',
      count: taxonomy?.count || 0
    }));

    return NextResponse.json({
      success: true,
      data: tags,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch tags',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/tags - Create new tag (Admin/Editor only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, slug, description = '' } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Tag name is required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    const tagSlug = slug || generateSlug(name);

    // Check if tag with same name or slug exists
    const existingTag = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .where(eq(terms.name, name))
      .limit(1);

    if (existingTag.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Tag with this name already exists' },
        { status: 400 }
      );
    }

    const existingSlug = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .where(eq(terms.slug, tagSlug))
      .limit(1);

    if (existingSlug.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Tag with this slug already exists' },
        { status: 400 }
      );
    }

    // Create term
    const termResult = await db.insert(terms).values({
      name,
      slug: tagSlug,
      term_group: 0
    });

    const termId = termResult.insertId;

    // Create taxonomy entry
    await db.insert(term_taxonomy).values({
      term_id: termId,
      taxonomy: 'post_tag',
      description,
      parent: 0,
      count: 0
    });

    return NextResponse.json({
      success: true,
      data: {
        id: termId,
        name,
        slug: tagSlug,
        description,
        count: 0
      }
    });

  } catch (error) {
    console.error('Error creating tag:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create tag',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/tags - Bulk update tags (Admin/Editor only)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, tagIds } = body;

    if (!action || !tagIds || !Array.isArray(tagIds)) {
      return NextResponse.json(
        { success: false, error: 'Action and tag IDs are required' },
        { status: 400 }
      );
    }

    if (action === 'delete') {
      // Delete tags and their taxonomy entries
      for (const tagId of tagIds) {
        // Delete term relationships first
        await db
          .delete(term_relationships)
          .where(eq(term_relationships.term_taxonomy_id, tagId));

        // Delete taxonomy entry
        await db
          .delete(term_taxonomy)
          .where(eq(term_taxonomy.term_id, tagId));

        // Delete term
        await db
          .delete(terms)
          .where(eq(terms.term_id, tagId));
      }

      return NextResponse.json({
        success: true,
        message: `${tagIds.length} tags deleted successfully`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error bulk updating tags:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update tags',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
