'use client';

import { useState, useCallback, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import EditableCodeBlock from '@/components/ui/editable-code-block';

interface UseEditableContentProps {
  initialContent: string;
  isEditable?: boolean;
  onContentUpdate?: (newContent: string) => void;
}

export const useEditableContent = ({
  initialContent,
  isEditable = false,
  onContentUpdate
}: UseEditableContentProps) => {
  const [content, setContent] = useState(initialContent);

  const updateCodeBlock = useCallback((blockId: string, newCode: string, newLanguage: string) => {
    const updatedContent = content.replace(
      new RegExp(`<pre[^>]*data-block-id="${blockId}"[^>]*>.*?</pre>`, 's'),
      `<pre class="bg-gray-900 text-gray-100 rounded-xl p-6 my-4 overflow-x-auto font-mono text-sm" data-block-id="${blockId}" data-language="${newLanguage}"><code class="language-${newLanguage}">${newCode}</code></pre>`
    );
    
    setContent(updatedContent);
    if (onContentUpdate) {
      onContentUpdate(updatedContent);
    }
  }, [content, onContentUpdate]);

  const processedContent = useMemo(() => {
    if (!isEditable) {
      return content;
    }

    // Add unique IDs to code blocks for editing
    let processedHtml = content;
    let blockCounter = 0;

    // Replace code blocks with editable versions
    processedHtml = processedHtml.replace(
      /<pre([^>]*class="[^"]*bg-gray-900[^"]*"[^>]*)><code([^>]*)>(.*?)<\/code><\/pre>/gs,
      (match, preAttrs, codeAttrs, codeContent) => {
        const blockId = `code-block-${blockCounter++}`;
        const languageMatch = codeAttrs.match(/class="[^"]*language-([^"\s]+)/);
        const language = languageMatch ? languageMatch[1] : 'javascript';
        
        return `<div data-code-block="${blockId}" data-language="${language}" data-code="${encodeURIComponent(codeContent.trim())}">${match}</div>`;
      }
    );

    return processedHtml;
  }, [content, isEditable]);

  const enhanceCodeBlocks = useCallback((containerRef: HTMLElement | null) => {
    if (!containerRef || !isEditable) return;

    const codeBlockContainers = containerRef.querySelectorAll('[data-code-block]');
    
    codeBlockContainers.forEach((container) => {
      const blockId = container.getAttribute('data-code-block');
      const language = container.getAttribute('data-language') || 'javascript';
      const encodedCode = container.getAttribute('data-code') || '';
      const code = decodeURIComponent(encodedCode);

      if (!blockId) return;

      // Create a wrapper div for the React component
      const wrapper = document.createElement('div');
      container.parentNode?.insertBefore(wrapper, container);
      
      // Hide the original code block
      (container as HTMLElement).style.display = 'none';

      // Render the editable code block
      const root = createRoot(wrapper);
      root.render(
        <EditableCodeBlock
          code={code}
          language={language}
          isEditable={true}
          onUpdate={(newCode, newLanguage) => {
            updateCodeBlock(blockId, newCode, newLanguage);
          }}
        />
      );
    });
  }, [isEditable, updateCodeBlock]);

  return {
    content: processedContent,
    enhanceCodeBlocks,
    updateContent: setContent
  };
};
