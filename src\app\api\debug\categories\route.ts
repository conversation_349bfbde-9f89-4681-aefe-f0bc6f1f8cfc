import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships, posts } from '@/lib/db/schema';
import { eq, count, and } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('🔍 Checking categories in database...');

    // Check if terms table exists and has data
    const allTerms = await db
      .select()
      .from(terms)
      .limit(10);

    console.log('📊 All terms:', allTerms);

    // Check term_taxonomy for categories specifically
    const categoryTaxonomies = await db
      .select({
        term_taxonomy_id: term_taxonomy.term_taxonomy_id,
        term_id: term_taxonomy.term_id,
        taxonomy: term_taxonomy.taxonomy,
        description: term_taxonomy.description,
        parent: term_taxonomy.parent,
        count: term_taxonomy.count,
        term_name: terms.name,
        term_slug: terms.slug
      })
      .from(term_taxonomy)
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .limit(20);

    console.log('📂 Categories found:', categoryTaxonomies);

    // Check for tags as well
    const tagTaxonomies = await db
      .select({
        term_taxonomy_id: term_taxonomy.term_taxonomy_id,
        term_id: term_taxonomy.term_id,
        taxonomy: term_taxonomy.taxonomy,
        description: term_taxonomy.description,
        parent: term_taxonomy.parent,
        count: term_taxonomy.count,
        term_name: terms.name,
        term_slug: terms.slug
      })
      .from(term_taxonomy)
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .limit(20);

    console.log('🏷️ Tags found:', tagTaxonomies);

    // Check term relationships (post-category connections)
    const termRelationships = await db
      .select()
      .from(term_relationships)
      .limit(10);

    console.log('🔗 Term relationships:', termRelationships);

    // Get posts with their categories
    const postsWithCategories = await db
      .select({
        post_id: posts.ID,
        post_title: posts.post_title,
        category_name: terms.name,
        category_slug: terms.slug,
        taxonomy: term_taxonomy.taxonomy
      })
      .from(posts)
      .leftJoin(term_relationships, eq(posts.ID, term_relationships.object_id))
      .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .limit(10);

    console.log('📝 Posts with categories:', postsWithCategories);

    // Count statistics
    const stats = {
      totalTerms: allTerms.length,
      totalCategories: categoryTaxonomies.length,
      totalTags: tagTaxonomies.length,
      totalRelationships: termRelationships.length,
      postsWithCategories: postsWithCategories.length
    };

    return NextResponse.json({
      success: true,
      message: 'Category check completed',
      stats,
      data: {
        terms: allTerms,
        categories: categoryTaxonomies,
        tags: tagTaxonomies,
        relationships: termRelationships,
        postsWithCategories
      }
    });

  } catch (error) {
    console.error('❌ Category check failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check categories',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST endpoint to create sample categories
export async function POST() {
  try {
    console.log('🔧 Creating sample categories...');

    // Sample categories to create
    const sampleCategories = [
      { name: 'Technology', slug: 'technology', description: 'Technology related posts' },
      { name: 'Health', slug: 'health', description: 'Health and wellness posts' },
      { name: 'Travel', slug: 'travel', description: 'Travel and adventure posts' },
      { name: 'Food', slug: 'food', description: 'Food and cooking posts' },
      { name: 'Lifestyle', slug: 'lifestyle', description: 'Lifestyle and personal posts' }
    ];

    const createdCategories = [];

    for (const category of sampleCategories) {
      // Check if term already exists
      const existingTerm = await db
        .select()
        .from(terms)
        .where(eq(terms.slug, category.slug))
        .limit(1);

      let termId;

      if (existingTerm.length === 0) {
        // Create new term
        const [newTerm] = await db
          .insert(terms)
          .values({
            name: category.name,
            slug: category.slug,
            term_group: 0
          });

        termId = newTerm.insertId;
      } else {
        termId = existingTerm[0].term_id;
      }

      // Check if taxonomy already exists
      const existingTaxonomy = await db
        .select()
        .from(term_taxonomy)
        .where(and(
          eq(term_taxonomy.term_id, termId),
          eq(term_taxonomy.taxonomy, 'category')
        ))
        .limit(1);

      if (existingTaxonomy.length === 0) {
        // Create taxonomy entry
        await db
          .insert(term_taxonomy)
          .values({
            term_id: termId,
            taxonomy: 'category',
            description: category.description,
            parent: 0,
            count: 0
          });
      }

      createdCategories.push({
        term_id: termId,
        name: category.name,
        slug: category.slug,
        description: category.description
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Sample categories created successfully',
      categories: createdCategories
    });

  } catch (error) {
    console.error('❌ Failed to create categories:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create categories',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
