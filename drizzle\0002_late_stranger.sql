CREATE TABLE `notifications` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`user_id` bigint unsigned NOT NULL,
	`type` varchar(50) NOT NULL,
	`title` varchar(255) NOT NULL,
	`message` text NOT NULL,
	`post_id` bigint unsigned,
	`post_title` varchar(255),
	`admin_id` bigint unsigned NOT NULL,
	`admin_name` varchar(100) NOT NULL,
	`is_read` boolean NOT NULL DEFAULT false,
	`created_at` datetime NOT NULL,
	`read_at` datetime,
	CONSTRAINT `notifications_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `trending_topics` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(255) NOT NULL,
	`search_query` varchar(255) NOT NULL,
	`traffic` varchar(50) NOT NULL,
	`source` varchar(50) NOT NULL DEFAULT 'google_trends',
	`country` varchar(10) NOT NULL DEFAULT 'US',
	`language` varchar(10) NOT NULL DEFAULT 'en',
	`rank` int NOT NULL DEFAULT 0,
	`created_at` datetime NOT NULL,
	`updated_at` datetime NOT NULL,
	`is_active` boolean NOT NULL DEFAULT true,
	CONSTRAINT `trending_topics_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE INDEX `user_idx` ON `notifications` (`user_id`);--> statement-breakpoint
CREATE INDEX `type_idx` ON `notifications` (`type`);--> statement-breakpoint
CREATE INDEX `created_idx` ON `notifications` (`created_at`);--> statement-breakpoint
CREATE INDEX `read_idx` ON `notifications` (`is_read`);--> statement-breakpoint
CREATE INDEX `source_country_idx` ON `trending_topics` (`source`,`country`);--> statement-breakpoint
CREATE INDEX `created_at_idx` ON `trending_topics` (`created_at`);--> statement-breakpoint
CREATE INDEX `rank_idx` ON `trending_topics` (`rank`);--> statement-breakpoint
CREATE INDEX `is_active_idx` ON `trending_topics` (`is_active`);