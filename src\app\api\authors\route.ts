import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, posts, usermeta } from '@/lib/db/schema';
import { eq, count, desc, and, sql, like, or } from 'drizzle-orm';

// GET /api/authors - Get all authors with their post counts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build base query
    let query = db
      .select({
        user: users,
        postCount: count(posts.ID)
      })
      .from(users)
      .leftJoin(posts, and(
        eq(posts.post_author, users.ID),
        eq(posts.post_status, 'publish'),
        eq(posts.post_type, 'post')
      ))
      .groupBy(users.ID)
      .having(sql`COUNT(${posts.ID}) > 0`);

    // Add search filter if provided
    if (search) {
      query = query.where(
        or(
          like(users.display_name, `%${search}%`),
          like(users.user_login, `%${search}%`),
          like(users.user_nicename, `%${search}%`)
        )
      );
    }

    // Get total count for pagination
    const totalQuery = db
      .select({ count: count() })
      .from(users)
      .leftJoin(posts, and(
        eq(posts.post_author, users.ID),
        eq(posts.post_status, 'publish'),
        eq(posts.post_type, 'post')
      ))
      .groupBy(users.ID)
      .having(sql`COUNT(${posts.ID}) > 0`);

    let totalCountQuery = totalQuery;
    if (search) {
      totalCountQuery = totalQuery.where(
        or(
          like(users.display_name, `%${search}%`),
          like(users.user_login, `%${search}%`),
          like(users.user_nicename, `%${search}%`)
        )
      );
    }

    const [authorsWithPosts, totalResult] = await Promise.all([
      query
        .orderBy(desc(count(posts.ID)))
        .limit(limit)
        .offset(offset),
      totalCountQuery
    ]);

    const total = totalResult.length;

    // Get additional metadata for each author
    const authorsWithMeta = await Promise.all(
      authorsWithPosts.map(async ({ user, postCount }) => {
        // Get bio
        const bioResult = await db
          .select()
          .from(usermeta)
          .where(eq(usermeta.user_id, user.ID))
          .where(eq(usermeta.meta_key, 'description'))
          .limit(1);

        const bio = bioResult.length > 0 ? bioResult[0].meta_value : '';

        // Get role from capabilities
        const capabilitiesResult = await db
          .select()
          .from(usermeta)
          .where(eq(usermeta.user_id, user.ID))
          .where(eq(usermeta.meta_key, 'wikify1h_capabilities'))
          .limit(1);

        let userRole = 'AUTHOR';
        if (capabilitiesResult.length > 0 && capabilitiesResult[0].meta_value) {
          const caps = capabilitiesResult[0].meta_value;
          if (caps.includes('administrator')) userRole = 'ADMIN';
          else if (caps.includes('editor')) userRole = 'EDITOR';
          else if (caps.includes('author')) userRole = 'AUTHOR';
        }

        return {
          id: user.ID,
          username: user.user_login,
          displayName: user.display_name || user.user_login,
          nicename: user.user_nicename,
          email: user.user_email,
          url: user.user_url,
          bio: bio || '',
          registered: user.user_registered,
          role: userRole,
          stats: {
            posts: postCount
          }
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: authorsWithMeta,
      total,
      pagination: {
        page,
        limit,
        hasMore: authorsWithPosts.length === limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching authors:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch authors',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
