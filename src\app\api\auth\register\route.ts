import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, usermeta, USER_STATUS } from '@/lib/db/schema';
import { eq, or } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { sendWelcomeEmail } from '@/lib/utils/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, email, password, displayName, mobile } = body;

    // Validate required fields
    if (!username || !email || !password || !mobile) {
      return NextResponse.json(
        { success: false, error: 'Username, email, password, and mobile number are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate mobile number format
    const mobileRegex = /^[\+]?[0-9\s\-\(\)]{10,20}$/;
    if (!mobileRegex.test(mobile.trim())) {
      return NextResponse.json(
        { success: false, error: 'Invalid mobile number format' },
        { status: 400 }
      );
    }

    // Validate username (alphanumeric and underscores only)
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { success: false, error: 'Username can only contain letters, numbers, and underscores' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(
        or(
          eq(users.user_login, username),
          eq(users.user_email, email)
        )
      )
      .limit(1);

    if (existingUser.length > 0) {
      const existingField = existingUser[0].user_login === username ? 'username' : 'email';
      return NextResponse.json(
        { success: false, error: `A user with this ${existingField} already exists` },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    const now = new Date();

    // Create user with pending status
    const result = await db.insert(users).values({
      user_login: username,
      user_pass: hashedPassword,
      user_nicename: username.toLowerCase().replace(/[^a-z0-9]/g, '-'),
      user_email: email,
      user_url: '',
      user_registered: now,
      user_activation_key: '',
      user_status: USER_STATUS.PENDING, // Set as pending
      display_name: displayName || username,
      user_mobile: mobile.trim()
    });

    const userId = result.insertId;

    // Set default user capabilities (AUTHOR role for new registrations)
    const capabilities = 'a:1:{s:6:"author";b:1;}';
    
    await db.insert(usermeta).values({
      user_id: userId,
      meta_key: 'wikify1h_capabilities',
      meta_value: capabilities
    });

    // Send welcome email to the new user
    try {
      const emailSent = await sendWelcomeEmail(email, username, displayName || username);
      if (!emailSent) {
        console.error('Failed to send welcome email to:', email);
        // Don't fail registration if email fails, just log it
      }
    } catch (emailError) {
      console.error('Welcome email error:', emailError);
      // Don't fail registration if email fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'Registration successful! Your account is pending approval. Check your email for more information.',
      data: {
        id: userId,
        username,
        email,
        displayName: displayName || username,
        status: 'pending',
        registered: now
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create account. Please try again.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
