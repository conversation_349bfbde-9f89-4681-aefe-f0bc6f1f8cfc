import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { uploadImage, uploadOptimizedImage } from '@/lib/cloudinary';
import { writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { tmpdir } from 'os';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'wikify-blog';
    const preset = formData.get('preset') as string || 'blog-post';
    const optimize = formData.get('optimize') !== 'false'; // Default to true
    const customWidth = formData.get('width') ? parseInt(formData.get('width') as string) : undefined;
    const customHeight = formData.get('height') ? parseInt(formData.get('height') as string) : undefined;
    const targetFileSize = formData.get('targetFileSize') ? parseInt(formData.get('targetFileSize') as string) : 70; // Default 70KB

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '')}`;

    // Create temporary file
    const tempFilePath = join(tmpdir(), filename);

    // Prepare upload options
    const uploadOptions = {
      folder: `${folder}/${session.user.id}`,
      public_id: filename,
      mimeType: file.type,
      customSize: customWidth && customHeight ? { width: customWidth, height: customHeight } : undefined,
      targetFileSize: targetFileSize,
    };

    try {
      // Write buffer to temporary file
      await writeFile(tempFilePath, buffer);

      let uploadResult;

      if (optimize && ['blog-post', 'featured-image', 'thumbnail', 'avatar', 'custom'].includes(preset)) {
        // Use optimized upload with preset
        uploadResult = await uploadOptimizedImage(
          tempFilePath,
          preset as 'blog-post' | 'featured-image' | 'thumbnail' | 'avatar' | 'custom',
          uploadOptions
        );
      } else {
        // Use regular upload
        uploadResult = await uploadImage(tempFilePath, {
          ...uploadOptions,
          optimize: optimize,
        });
      }

      // Clean up temporary file
      await unlink(tempFilePath);

      if (!uploadResult.success) {
        return NextResponse.json(
          { success: false, error: uploadResult.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          url: uploadResult.data?.url,
          public_id: uploadResult.data?.public_id,
          width: uploadResult.data?.width,
          height: uploadResult.data?.height,
          format: uploadResult.data?.format,
          size: uploadResult.data?.bytes,
          original_width: uploadResult.data?.original_width,
          original_height: uploadResult.data?.original_height,
          compression_ratio: uploadResult.data?.compression_ratio,
          optimized: optimize,
          preset: preset,
        }
      });
    } catch (tempFileError) {
      // If temp file approach fails, try buffer approach
      console.log('Temp file approach failed, trying buffer approach:', tempFileError);

      let uploadResult;

      if (optimize && ['blog-post', 'featured-image', 'thumbnail', 'avatar', 'custom'].includes(preset)) {
        // Use optimized upload with preset
        uploadResult = await uploadOptimizedImage(
          buffer,
          preset as 'blog-post' | 'featured-image' | 'thumbnail' | 'avatar' | 'custom',
          uploadOptions
        );
      } else {
        // Use regular upload
        uploadResult = await uploadImage(buffer, {
          ...uploadOptions,
          optimize: optimize,
        });
      }

      if (!uploadResult.success) {
        return NextResponse.json(
          { success: false, error: uploadResult.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          url: uploadResult.data?.url,
          public_id: uploadResult.data?.public_id,
          width: uploadResult.data?.width,
          height: uploadResult.data?.height,
          format: uploadResult.data?.format,
          size: uploadResult.data?.bytes,
          original_width: uploadResult.data?.original_width,
          original_height: uploadResult.data?.original_height,
          compression_ratio: uploadResult.data?.compression_ratio,
          optimized: optimize,
          preset: preset,
        }
      });
    }

  } catch (error) {
    console.error('Upload error:', error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
