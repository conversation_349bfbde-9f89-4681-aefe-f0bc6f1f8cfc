'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Comment from './comment';

interface CommentData {
  id: number;
  content: string;
  date: string;
  approved: boolean;
  parentId?: number | null;
  author: {
    id?: number;
    username?: string;
    displayName?: string;
    email?: string;
    name?: string;
  };
}

interface CommentsSectionProps {
  postId: number;
  commentsEnabled?: boolean;
}

const CommentsSection: React.FC<CommentsSectionProps> = ({
  postId,
  commentsEnabled = true,
}) => {
  const { data: session } = useSession();
  const [comments, setComments] = useState<CommentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [authorName, setAuthorName] = useState('');
  const [authorEmail, setAuthorEmail] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchComments();
  }, [postId]);

  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/comments?post_id=${postId}&status=approved`);
      const result = await response.json();

      if (result.success) {
        setComments(result.data);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!newComment.trim()) {
      setError('Please enter a comment');
      return;
    }

    if (!session && (!authorName.trim() || !authorEmail.trim())) {
      setError('Please enter your name and email');
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId,
          content: newComment,
          authorName: !session ? authorName : undefined,
          authorEmail: !session ? authorEmail : undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Add new comment to the list
        setComments(prev => [result.data, ...prev]);
        setNewComment('');
        setAuthorName('');
        setAuthorEmail('');
      } else {
        setError(result.error || 'Failed to post comment');
      }
    } catch (error) {
      setError('An error occurred while posting your comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleReply = async (parentId: number, content: string) => {
    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId,
          content,
          parentId,
          authorName: !session ? authorName : undefined,
          authorEmail: !session ? authorEmail : undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Refresh comments to show the new reply
        fetchComments();
      } else {
        throw new Error(result.error || 'Failed to post reply');
      }
    } catch (error) {
      console.error('Error posting reply:', error);
      throw error;
    }
  };

  // Organize comments into threads
  const organizeComments = (comments: CommentData[]) => {
    const commentMap = new Map<number, CommentData & { replies: CommentData[] }>();
    const rootComments: (CommentData & { replies: CommentData[] })[] = [];

    // First pass: create map of all comments
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // Second pass: organize into threads
    comments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;
      
      if (comment.parentId && commentMap.has(comment.parentId)) {
        commentMap.get(comment.parentId)!.replies.push(commentWithReplies);
      } else {
        rootComments.push(commentWithReplies);
      }
    });

    return rootComments;
  };

  const renderComment = (comment: CommentData & { replies: CommentData[] }, level = 0) => (
    <div key={comment.id}>
      <Comment
        comment={comment}
        onReply={handleReply}
        level={level}
      />
      {comment.replies.map(reply => renderComment(reply, level + 1))}
    </div>
  );

  if (!commentsEnabled) {
    return (
      <Card className="mt-8">
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Comments are disabled for this post.</p>
        </CardContent>
      </Card>
    );
  }

  const organizedComments = organizeComments(comments);

  return (
    <div className="mt-8 space-y-6">
      {/* Comments Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Comments ({comments.length})
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          {/* New Comment Form */}
          <form onSubmit={handleCommentSubmit} className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Leave a Comment</h3>
            
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            {!session && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Your Name"
                  value={authorName}
                  onChange={(e) => setAuthorName(e.target.value)}
                  required
                  placeholder="Enter your name"
                />
                <Input
                  label="Your Email"
                  type="email"
                  value={authorEmail}
                  onChange={(e) => setAuthorEmail(e.target.value)}
                  required
                  placeholder="Enter your email"
                />
              </div>
            )}
            
            <Textarea
              label="Your Comment"
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              required
              placeholder="Write your comment here..."
              rows={4}
            />
            
            <Button
              type="submit"
              disabled={submitting}
              loading={submitting}
            >
              Post Comment
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Comments List */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading comments...</span>
        </div>
      ) : organizedComments.length > 0 ? (
        <div className="space-y-4">
          {organizedComments.map(comment => renderComment(comment))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No comments yet. Be the first to comment!</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CommentsSection;
