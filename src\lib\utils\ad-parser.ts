/**
 * Ad Code Parser Utility
 * Parses different types of ad codes and extracts relevant information
 */

export interface ParsedAdData {
  type: 'advertica' | 'adsense' | 'generic';
  dataDomain?: string;
  dataAffquery?: string;
  dataWidth?: string;
  dataHeight?: string;
  className?: string;
  scriptSrc?: string;
  originalCode: string;
}

/**
 * Parse Advertica ad code
 */
export function parseAdverticaAd(adCode: string): ParsedAdData | null {
  try {
    // Look for ins element with Advertica attributes
    const insMatch = adCode.match(/<ins[^>]*data-domain[^>]*>/i);
    if (!insMatch) return null;

    const insElement = insMatch[0];
    
    // Extract attributes
    const dataDomain = extractAttribute(insElement, 'data-domain');
    const dataAffquery = extractAttribute(insElement, 'data-affquery');
    const dataWidth = extractAttribute(insElement, 'data-width');
    const dataHeight = extractAttribute(insElement, 'data-height');
    const className = extractAttribute(insElement, 'class');

    // Extract script src if present
    const scriptMatch = adCode.match(/<script[^>]*src="([^"]*)"[^>]*>/i);
    const scriptSrc = scriptMatch ? scriptMatch[1] : undefined;

    if (!dataDomain) return null;

    return {
      type: 'advertica',
      dataDomain,
      dataAffquery,
      dataWidth: dataWidth || '300',
      dataHeight: dataHeight || '250',
      className,
      scriptSrc,
      originalCode: adCode
    };
  } catch (error) {
    console.error('Error parsing Advertica ad:', error);
    return null;
  }
}

/**
 * Parse Google AdSense ad code
 */
export function parseAdSenseAd(adCode: string): ParsedAdData | null {
  try {
    // Look for AdSense patterns
    if (!adCode.includes('adsbygoogle') && !adCode.includes('googlesyndication')) {
      return null;
    }

    return {
      type: 'adsense',
      originalCode: adCode
    };
  } catch (error) {
    console.error('Error parsing AdSense ad:', error);
    return null;
  }
}

/**
 * Parse any ad code and determine its type
 */
export function parseAdCode(adCode: string): ParsedAdData {
  if (!adCode || !adCode.trim()) {
    return {
      type: 'generic',
      originalCode: adCode
    };
  }

  // Try Advertica first
  const adverticaData = parseAdverticaAd(adCode);
  if (adverticaData) {
    return adverticaData;
  }

  // Try AdSense
  const adsenseData = parseAdSenseAd(adCode);
  if (adsenseData) {
    return adsenseData;
  }

  // Default to generic
  return {
    type: 'generic',
    originalCode: adCode
  };
}

/**
 * Extract attribute value from HTML string
 */
function extractAttribute(htmlString: string, attributeName: string): string | undefined {
  const regex = new RegExp(`${attributeName}="([^"]*)"`, 'i');
  const match = htmlString.match(regex);
  return match ? match[1] : undefined;
}

/**
 * Check if ad code is likely to have CORS issues
 */
export function hasCorsIssues(adCode: string): boolean {
  const corsProblematicDomains = [
    'data684.click',
    'advertica.com',
    // Add other domains known to have CORS issues
  ];

  return corsProblematicDomains.some(domain => 
    adCode.includes(domain)
  );
}

/**
 * Get ad dimensions from parsed data
 */
export function getAdDimensions(parsedAd: ParsedAdData): { width: number; height: number } {
  const width = parseInt(parsedAd.dataWidth || '300');
  const height = parseInt(parsedAd.dataHeight || '250');
  
  return {
    width: isNaN(width) ? 300 : width,
    height: isNaN(height) ? 250 : height
  };
}

/**
 * Generate a fallback ad for CORS-blocked ads
 */
export function generateFallbackAd(parsedAd: ParsedAdData): string {
  const { width, height } = getAdDimensions(parsedAd);
  
  return `
    <div style="
      width: ${width}px;
      height: ${height}px;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
      border: 2px dashed #ccc;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-family: Arial, sans-serif;
      color: #666;
      text-align: center;
      padding: 20px;
      box-sizing: border-box;
    ">
      <div style="font-size: 16px; margin-bottom: 10px;">📢</div>
      <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px;">
        Advertisement Space
      </div>
      <div style="font-size: 12px; opacity: 0.7;">
        ${parsedAd.type === 'advertica' ? 'Advertica Network' : 'Ad Network'}
      </div>
      <div style="font-size: 10px; margin-top: 10px; opacity: 0.5;">
        ${width} × ${height}
      </div>
    </div>
  `;
}
