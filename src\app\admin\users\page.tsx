'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/admin-layout';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';
import {
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  Mail,
  Calendar,
  Shield,
  MoreHorizontal,
  Filter,
  AlertCircle,
  User,
  Check,
  X
} from 'lucide-react';

interface User {
  id: number;
  username: string;
  email: string;
  displayName: string;
  registered: string;
  status: number;
  role: string;
  url?: string;
  canInsertAds?: boolean;
  stats?: {
    posts: number;
    comments: number;
  };
}

interface UserFormData {
  username: string;
  email: string;
  password: string;
  displayName: string;
  role: string;
  url: string;
  status: number;
  changeStatus?: boolean;
  canInsertAds?: boolean;
}

interface FormErrors {
  username?: string;
  email?: string;
  password?: string;
  displayName?: string;
  general?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    password: '',
    displayName: '',
    role: 'AUTHOR',
    url: '',
    status: 0,
    changeStatus: false,
    canInsertAds: false
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [currentPage, searchTerm, selectedRole]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(selectedRole && { role: selectedRole }),
        includeAdsPermissions: 'true'
      });

      const response = await fetch(`/api/users?${params}`);
      const result = await response.json();

      if (result.success) {
        setUsers(result.data);
        setTotalPages(result.pagination.totalPages);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async () => {
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        setShowCreateModal(false);
        setFormData({
          username: '',
          email: '',
          password: '',
          displayName: '',
          role: 'AUTHOR',
          url: ''
        });
        fetchUsers();
      } else {
        alert(result.error || 'Failed to create user');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Failed to create user');
    }
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Username validation
    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.username)) {
      errors.username = 'Username can only contain letters, numbers, hyphens, and underscores';
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Password validation (only if password is provided)
    if (formData.password && formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    // Display name validation
    if (!formData.displayName.trim()) {
      errors.displayName = 'Display name is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEditUser = async () => {
    if (!selectedUser) return;

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setFormErrors({});

      // Build update data with only changed fields
      const updateData: any = {};

      if (formData.username !== selectedUser.username) {
        updateData.username = formData.username;
      }
      if (formData.email !== selectedUser.email) {
        updateData.email = formData.email;
      }
      if (formData.displayName !== selectedUser.displayName) {
        updateData.displayName = formData.displayName;
      }
      if (formData.role !== selectedUser.role) {
        updateData.role = formData.role;
      }
      if (formData.url !== (selectedUser.url || '')) {
        updateData.url = formData.url;
      }
      // Only include status if user explicitly wants to change it
      if (formData.changeStatus && formData.status !== selectedUser.status) {
        updateData.status = formData.status;
      }
      if (formData.password) {
        updateData.password = formData.password;
      }

      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      const result = await response.json();

      if (result.success) {
        setShowEditModal(false);
        setSelectedUser(null);
        setFormData({
          username: '',
          email: '',
          password: '',
          displayName: '',
          role: 'AUTHOR',
          url: '',
          status: 0,
          changeStatus: false
        });
        setFormErrors({});
        fetchUsers();

        // Show success message
        alert('User updated successfully!');
      } else {
        setFormErrors({ general: result.error || 'Failed to update user' });
      }
    } catch (error) {
      console.error('Error updating user:', error);
      setFormErrors({ general: 'Failed to update user. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        fetchUsers();
        alert('User deleted successfully!');
      } else {
        alert(result.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Failed to delete user');
    }
  };

  const handleQuickRoleChange = async (userId: number, newRole: string, currentUser: User) => {
    if (!confirm(`Are you sure you want to change ${currentUser.username}'s role to ${newRole}?`)) return;

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role: newRole })
      });

      const result = await response.json();

      if (result.success) {
        fetchUsers();
        alert(`User role updated to ${newRole} successfully!`);
      } else {
        alert(result.error || 'Failed to update user role');
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      alert('Failed to update user role');
    }
  };

  const handleToggleStatus = async (userId: number, currentStatus: number, currentUser: User) => {
    const newStatus = currentStatus === 0 ? 1 : 0;
    const statusText = newStatus === 0 ? 'activate' : 'suspend';

    if (!confirm(`Are you sure you want to ${statusText} ${currentUser.username}?`)) return;

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });

      const result = await response.json();

      if (result.success) {
        fetchUsers();
        alert(`User ${statusText}d successfully!`);
      } else {
        alert(result.error || `Failed to ${statusText} user`);
      }
    } catch (error) {
      console.error(`Error ${statusText}ing user:`, error);
      alert(`Failed to ${statusText} user`);
    }
  };

  const handleToggleAdsPermission = async (userId: number, currentPermission: boolean, username: string) => {
    const action = currentPermission ? 'revoke' : 'grant';

    if (!confirm(`Are you sure you want to ${action} ads insert permission for ${username}?`)) return;

    try {
      const response = await fetch(`/api/admin/users/${userId}/ads-permission`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ canInsertAds: !currentPermission })
      });

      const result = await response.json();

      if (result.success) {
        fetchUsers();
        alert(`Ads permission ${action}ed successfully!`);
      } else {
        alert(result.error || `Failed to ${action} ads permission`);
      }
    } catch (error) {
      console.error(`Error ${action}ing ads permission:`, error);
      alert(`Failed to ${action} ads permission`);
    }
  };

  const openEditModal = (user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      password: '',
      displayName: user.displayName,
      role: user.role,
      url: user.url || '',
      status: user.status,
      changeStatus: false,
      canInsertAds: user.canInsertAds || false
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800 border border-red-200';
      case 'EDITOR': return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'AUTHOR': return 'bg-green-100 text-green-800 border border-green-200';
      default: return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN': return <Shield className="w-3 h-3" />;
      case 'EDITOR': return <Edit className="w-3 h-3" />;
      case 'AUTHOR': return <User className="w-3 h-3" />;
      default: return <User className="w-3 h-3" />;
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'Full system access';
      case 'EDITOR': return 'Content management';
      case 'AUTHOR': return 'Content creation';
      default: return 'Basic access';
    }
  };

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
              <p className="text-gray-600 mt-2">
                Manage user accounts, roles, and permissions.
              </p>
            </div>
            <Button 
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Roles</option>
                  <option value="ADMIN">Admin</option>
                  <option value="EDITOR">Editor</option>
                  <option value="AUTHOR">Author</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Users ({users.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">User</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Registered</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4">
                          <div>
                            <div className="font-medium text-gray-900">{user.displayName}</div>
                            <div className="text-sm text-gray-500">@{user.username}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Mail className="w-3 h-3 mr-1" />
                              {user.email}
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex flex-col">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeColor(user.role)}`}>
                              {getRoleIcon(user.role)}
                              <span className="ml-1">{user.role}</span>
                            </span>
                            <span className="text-xs text-gray-500 mt-1">
                              {getRoleDescription(user.role)}
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center text-sm text-gray-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(user.registered).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            user.status === 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {user.status === 0 ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end space-x-1">
                            {/* Quick Role Change Dropdown */}
                            <div className="relative group">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-600 hover:text-blue-700"
                                title="Quick Role Change"
                              >
                                <Shield className="w-4 h-4" />
                              </Button>
                              <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[140px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                {['AUTHOR', 'EDITOR', 'ADMIN'].filter(role => role !== user.role).map(role => (
                                  <button
                                    key={role}
                                    onClick={() => handleQuickRoleChange(user.id, role, user)}
                                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center"
                                  >
                                    {getRoleIcon(role)}
                                    <span className="ml-2">Make {role}</span>
                                  </button>
                                ))}
                              </div>
                            </div>

                            {/* Toggle Status */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleStatus(user.id, user.status, user)}
                              className={user.status === 0 ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
                              title={user.status === 0 ? "Suspend User" : "Activate User"}
                            >
                              {user.status === 0 ? (
                                <X className="w-4 h-4" />
                              ) : (
                                <Check className="w-4 h-4" />
                              )}
                            </Button>

                            {/* Toggle Ads Permission */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleAdsPermission(user.id, user.canInsertAds || false, user.username)}
                              className={user.canInsertAds ? "text-green-600 hover:text-green-700" : "text-gray-400 hover:text-gray-600"}
                              title={user.canInsertAds ? "Revoke Ads Permission" : "Grant Ads Permission"}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </Button>

                            {/* Edit User */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditModal(user)}
                              className="text-gray-600 hover:text-gray-700"
                              title="Edit User"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>

                            {/* Delete User */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-700"
                              title="Delete User"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {users.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No users found
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create User Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New User"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Username
              </label>
              <Input
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                placeholder="Enter username"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="Enter email"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                placeholder="Enter password"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Name
              </label>
              <Input
                value={formData.displayName}
                onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                placeholder="Enter display name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role
              </label>
              <select
                value={formData.role}
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="AUTHOR">Author</option>
                <option value="EDITOR">Editor</option>
                <option value="ADMIN">Admin</option>
              </select>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateUser}>
                Create User
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit User Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormErrors({});
            setIsSubmitting(false);
          }}
          title={`Edit User: ${selectedUser?.username || ''}`}
          size="lg"
        >
          <div className="space-y-6">
            {/* General Error Message */}
            {formErrors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 text-red-800">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">Error</span>
                </div>
                <p className="text-red-700 text-sm mt-1">{formErrors.general}</p>
              </div>
            )}

            {/* User Info Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                User Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Username *
                  </label>
                  <Input
                    value={formData.username}
                    onChange={(e) => {
                      setFormData({ ...formData, username: e.target.value });
                      if (formErrors.username) {
                        setFormErrors({ ...formErrors, username: undefined });
                      }
                    }}
                    placeholder="Enter username"
                    error={formErrors.username}
                    className={formErrors.username ? 'border-red-300' : ''}
                  />
                  {formErrors.username && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.username}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => {
                      setFormData({ ...formData, email: e.target.value });
                      if (formErrors.email) {
                        setFormErrors({ ...formErrors, email: undefined });
                      }
                    }}
                    placeholder="Enter email address"
                    error={formErrors.email}
                    className={formErrors.email ? 'border-red-300' : ''}
                  />
                  {formErrors.email && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name *
                  </label>
                  <Input
                    value={formData.displayName}
                    onChange={(e) => {
                      setFormData({ ...formData, displayName: e.target.value });
                      if (formErrors.displayName) {
                        setFormErrors({ ...formErrors, displayName: undefined });
                      }
                    }}
                    placeholder="Enter display name"
                    error={formErrors.displayName}
                    className={formErrors.displayName ? 'border-red-300' : ''}
                  />
                  {formErrors.displayName && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.displayName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website URL
                  </label>
                  <Input
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                    placeholder="https://example.com"
                  />
                </div>
              </div>
            </div>

            {/* Security Section */}
            <div className="bg-yellow-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Security & Permissions
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    New Password
                  </label>
                  <Input
                    type="password"
                    value={formData.password}
                    onChange={(e) => {
                      setFormData({ ...formData, password: e.target.value });
                      if (formErrors.password) {
                        setFormErrors({ ...formErrors, password: undefined });
                      }
                    }}
                    placeholder="Leave blank to keep current password"
                    error={formErrors.password}
                    className={formErrors.password ? 'border-red-300' : ''}
                  />
                  {formErrors.password && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.password}</p>
                  )}
                  <p className="text-gray-500 text-xs mt-1">
                    Minimum 6 characters required
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    User Role
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="AUTHOR">Author - Can create and edit own posts</option>
                    <option value="EDITOR">Editor - Can edit all posts and manage content</option>
                    <option value="ADMIN">Admin - Full system access</option>
                  </select>
                  <p className="text-gray-500 text-xs mt-1">
                    {formData.role === 'AUTHOR' && 'Can create and manage their own posts'}
                    {formData.role === 'EDITOR' && 'Can manage all content and moderate comments'}
                    {formData.role === 'ADMIN' && 'Full administrative access to all features'}
                  </p>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center mb-3">
                  <input
                    type="checkbox"
                    id="changeStatus"
                    checked={formData.changeStatus}
                    onChange={(e) => setFormData({ ...formData, changeStatus: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="changeStatus" className="text-sm font-medium text-gray-700">
                    Change Account Status
                  </label>
                </div>

                {formData.changeStatus && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Status
                    </label>
                    <div className="flex items-center space-x-6">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="status"
                          value={1}
                          checked={formData.status === 1}
                          onChange={(e) => setFormData({ ...formData, status: parseInt(e.target.value) })}
                          className="mr-2"
                        />
                        <span className="text-sm text-green-700 font-medium">Approved</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="status"
                          value={0}
                          checked={formData.status === 0}
                          onChange={(e) => setFormData({ ...formData, status: parseInt(e.target.value) })}
                          className="mr-2"
                        />
                        <span className="text-sm text-orange-700 font-medium">Pending</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="status"
                          value={2}
                          checked={formData.status === 2}
                          onChange={(e) => setFormData({ ...formData, status: parseInt(e.target.value) })}
                          className="mr-2"
                        />
                        <span className="text-sm text-red-700 font-medium">Rejected</span>
                      </label>
                    </div>
                  </div>
                )}

                {!formData.changeStatus && (
                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    <strong>Current Status:</strong> {
                      selectedUser?.status === 1 ? 'Approved' :
                      selectedUser?.status === 0 ? 'Pending' :
                      selectedUser?.status === 2 ? 'Rejected' : 'Unknown'
                    }
                    <br />
                    <span className="text-xs">Check the box above to change the account status</span>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setFormErrors({});
                  setIsSubmitting(false);
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleEditUser}
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Updating...
                  </div>
                ) : (
                  'Update User'
                )}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </AdminLayout>
  );
}
