import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { grantAdsPermission } from '@/lib/utils/ads-permissions';

/**
 * POST /api/user/grant-ads-permission
 * Allows users to grant themselves ad permissions for testing purposes
 * In production, this should be restricted to admins only
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }

    // In development, allow any user to grant themselves ad permissions
    // In production, restrict this to admins only
    if (process.env.NODE_ENV === 'production' && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required in production.' },
        { status: 403 }
      );
    }

    // Grant ads permission
    const success = await grantAdsPermission(userId);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to grant ads permission' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Ads permission granted successfully',
      data: {
        userId,
        username: session.user.username,
        canInsertAds: true
      }
    });

  } catch (error) {
    console.error('Error granting ads permission:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to grant ads permission',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
