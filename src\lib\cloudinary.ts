import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export default cloudinary;

// Default image optimization settings for 30-100KB file size
const DEFAULT_OPTIMIZATION = {
  // Resize to 1280x720 with smart cropping
  width: 1280,
  height: 720,
  crop: 'fill',
  gravity: 'auto',
  // Convert to WebP format for better compression
  format: 'webp',
  // Very aggressive quality optimization for smaller file size (30-100KB)
  quality: '40',
  // Additional optimizations for smaller file size
  fetch_format: 'webp',
  flags: ['progressive', 'lossy'],
  // Force WebP format and disable auto format
  f_auto: false,
  dpr: '1.0',
};

// Upload image to Cloudinary with optimization
export async function uploadImage(
  file: Buffer | string,
  options: {
    folder?: string;
    public_id?: string;
    transformation?: any;
    mimeType?: string;
    optimize?: boolean;
    customSize?: { width: number; height: number };
  } = {}
) {
  try {
    let uploadData: string;

    if (Buffer.isBuffer(file)) {
      // Detect MIME type from buffer or use provided mimeType
      const mimeType = options.mimeType || detectMimeType(file) || 'image/jpeg';
      // Convert Buffer to base64 data URI
      uploadData = `data:${mimeType};base64,${file.toString('base64')}`;
    } else if (typeof file === 'string') {
      // Check if it's a file path or already a data URI
      if (file.startsWith('data:')) {
        uploadData = file;
      } else {
        // It's a file path, Cloudinary can handle this directly
        uploadData = file;
      }
    } else {
      throw new Error('Invalid file type');
    }

    // Prepare transformation settings
    let transformation = options.transformation;

    // Apply default optimization if not disabled
    if (options.optimize !== false) {
      const optimizationSettings = { ...DEFAULT_OPTIMIZATION };

      // Use custom size if provided
      if (options.customSize) {
        optimizationSettings.width = options.customSize.width;
        optimizationSettings.height = options.customSize.height;
      }

      // Merge with any custom transformations
      transformation = transformation
        ? [optimizationSettings, transformation]
        : optimizationSettings;
    }

    const result = await cloudinary.uploader.upload(uploadData, {
      folder: options.folder || 'wikify-blog',
      public_id: options.public_id,
      transformation: transformation,
      resource_type: 'auto',
      // Additional upload options for optimization
      use_filename: true,
      unique_filename: true,
      overwrite: false,
      // Force WebP format and very aggressive compression for small file size
      format: 'webp',
      quality: '40',
      fetch_format: 'webp',
      flags: 'progressive.lossy',
      dpr: '1.0',
    });

    return {
      success: true,
      data: {
        public_id: result.public_id,
        url: result.secure_url,
        width: result.width,
        height: result.height,
        format: result.format,
        bytes: result.bytes,
        // Include original dimensions before transformation
        original_width: result.original_width || result.width,
        original_height: result.original_height || result.height,
        // Compression info
        compression_ratio: result.original_bytes && result.bytes
          ? ((result.original_bytes - result.bytes) / result.original_bytes * 100).toFixed(1)
          : null,
      },
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

// Simple MIME type detection based on file signature
function detectMimeType(buffer: Buffer): string | null {
  if (buffer.length < 4) return null;

  // Check for common image file signatures
  const signature = buffer.toString('hex', 0, 4).toUpperCase();

  if (signature.startsWith('FFD8')) return 'image/jpeg';
  if (signature.startsWith('8950')) return 'image/png';
  if (signature.startsWith('4749')) return 'image/gif';
  if (signature.startsWith('5249')) return 'image/webp';

  return 'image/jpeg'; // Default fallback
}

// Extract Cloudinary public_id from image URL
export function extractPublicId(imageUrl: string): string | null {
  if (!imageUrl) return null;

  try {
    // Handle Cloudinary URLs
    const cloudinaryMatch = imageUrl.match(/cloudinary\.com\/[^\/]+\/image\/upload\/(?:v\d+\/)?(.+?)(?:\.[^.]+)?$/);
    if (cloudinaryMatch) {
      return cloudinaryMatch[1];
    }

    // Handle direct public_id (if already extracted)
    if (!imageUrl.includes('/') && !imageUrl.includes('.')) {
      return imageUrl;
    }

    return null;
  } catch (error) {
    console.error('Error extracting public_id from URL:', imageUrl, error);
    return null;
  }
}

// Delete image from Cloudinary
export async function deleteImage(publicId: string) {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return {
      success: result.result === 'ok',
      data: result,
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
    };
  }
}

// Delete multiple images from Cloudinary
export async function deleteMultipleImages(publicIds: string[]) {
  try {
    const results = await Promise.allSettled(
      publicIds.map(publicId => deleteImage(publicId))
    );

    const successful = results.filter(result =>
      result.status === 'fulfilled' && result.value.success
    ).length;

    const failed = results.length - successful;

    return {
      success: failed === 0,
      total: results.length,
      successful,
      failed,
      results
    };
  } catch (error) {
    console.error('Cloudinary bulk delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bulk delete failed',
      total: publicIds.length,
      successful: 0,
      failed: publicIds.length
    };
  }
}

// Generate optimized image URL
export function getOptimizedImageUrl(
  publicId: string,
  options: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string | number;
    format?: string;
  } = {}
) {
  return cloudinary.url(publicId, {
    width: options.width,
    height: options.height,
    crop: options.crop || 'fill',
    quality: options.quality || 'auto',
    format: options.format || 'auto',
    fetch_format: 'auto',
  });
}

// Upload with aggressive compression for small file sizes (30-100KB)
export async function uploadOptimizedImage(
  file: Buffer | string,
  preset: 'blog-post' | 'featured-image' | 'thumbnail' | 'avatar' | 'custom',
  options: {
    folder?: string;
    public_id?: string;
    mimeType?: string;
    customSize?: { width: number; height: number };
    targetFileSize?: number; // Target file size in KB (default: 70KB)
  } = {}
) {
  const targetSize = options.targetFileSize || 70; // Default 70KB

  const presets = {
    'blog-post': {
      width: 1280,
      height: 720,
      crop: 'fill',
      gravity: 'auto',
      format: 'webp',
      quality: targetSize <= 50 ? '30' : targetSize <= 70 ? '40' : '50',
      fetch_format: 'webp',
      flags: ['progressive', 'lossy'],
      dpr: '1.0',
    },
    'featured-image': {
      width: 1280,
      height: 720,
      crop: 'fill',
      gravity: 'center',
      format: 'webp',
      quality: targetSize <= 50 ? '30' : targetSize <= 70 ? '40' : '50',
      fetch_format: 'webp',
      flags: ['progressive', 'lossy'],
      dpr: '1.0',
    },
    'thumbnail': {
      width: 300,
      height: 200,
      crop: 'fill',
      gravity: 'auto',
      format: 'webp',
      quality: '35',
      fetch_format: 'webp',
      flags: ['progressive', 'lossy'],
      dpr: '1.0',
    },
    'avatar': {
      width: 400,
      height: 400,
      crop: 'fill',
      gravity: 'face',
      format: 'webp',
      quality: '40',
      fetch_format: 'webp',
      flags: ['progressive', 'lossy'],
      dpr: '1.0',
    },
    'custom': options.customSize ? {
      width: options.customSize.width,
      height: options.customSize.height,
      crop: 'fill',
      gravity: 'auto',
      format: 'webp',
      quality: targetSize <= 50 ? '30' : targetSize <= 70 ? '40' : '50',
      fetch_format: 'webp',
      flags: ['progressive', 'lossy'],
      dpr: '1.0',
    } : DEFAULT_OPTIMIZATION,
  };

  return uploadImage(file, {
    ...options,
    transformation: presets[preset],
    optimize: true,
  });
}

// Generate image transformations for different sizes with WebP optimization
export function generateImageVariants(publicId: string) {
  return {
    thumbnail: getOptimizedImageUrl(publicId, {
      width: 150,
      height: 150,
      crop: 'thumb',
      format: 'webp',
      quality: 'auto:good'
    }),
    small: getOptimizedImageUrl(publicId, {
      width: 300,
      height: 200,
      format: 'webp',
      quality: 'auto:good'
    }),
    medium: getOptimizedImageUrl(publicId, {
      width: 600,
      height: 400,
      format: 'webp',
      quality: 'auto:good'
    }),
    large: getOptimizedImageUrl(publicId, {
      width: 1280,
      height: 720,
      format: 'webp',
      quality: 'auto:low'
    }),
    original: cloudinary.url(publicId, {
      format: 'webp',
      quality: 'auto:best'
    }),
  };
}

// Get responsive image URLs for different screen sizes
export function getResponsiveImageUrls(publicId: string) {
  return {
    mobile: getOptimizedImageUrl(publicId, {
      width: 480,
      height: 252,
      format: 'webp',
      quality: 'auto:good'
    }),
    tablet: getOptimizedImageUrl(publicId, {
      width: 768,
      height: 403,
      format: 'webp',
      quality: 'auto:good'
    }),
    desktop: getOptimizedImageUrl(publicId, {
      width: 1280,
      height: 720,
      format: 'webp',
      quality: 'auto:low'
    }),
    retina: getOptimizedImageUrl(publicId, {
      width: 2560,
      height: 1440,
      format: 'webp',
      quality: 'auto:low'
    }),
  };
}

// Transform existing Cloudinary image URL to optimized version
export function optimizeExistingImage(imageUrl: string, options: {
  width?: number;
  height?: number;
  format?: string;
  quality?: string;
  crop?: string;
} = {}) {
  const publicId = extractPublicId(imageUrl);
  if (!publicId) return imageUrl;

  const defaultOptions = {
    width: 1280,
    height: 720,
    format: 'webp',
    quality: 'auto:low',
    crop: 'fill',
    ...options
  };

  return getOptimizedImageUrl(publicId, defaultOptions);
}

// Get image info from Cloudinary
export async function getImageInfo(publicId: string) {
  try {
    const result = await cloudinary.api.resource(publicId);
    return {
      success: true,
      data: {
        public_id: result.public_id,
        width: result.width,
        height: result.height,
        format: result.format,
        bytes: result.bytes,
        url: result.secure_url,
        created_at: result.created_at,
      }
    };
  } catch (error) {
    console.error('Error getting image info:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get image info'
    };
  }
}

// Estimate file size for optimization settings
export function estimateOptimizedFileSize(
  originalSizeBytes: number,
  targetDimensions: { width: number; height: number },
  quality: string | number
): { estimatedBytes: number; estimatedKB: number; compressionRatio: number } {
  // Base compression from WebP format (typically 25-35% smaller than JPEG)
  const webpCompression = 0.7; // 30% reduction

  // Quality-based compression
  const qualityNum = typeof quality === 'string' ?
    (quality === 'auto:low' ? 30 : quality === 'auto:good' ? 50 : quality === 'auto:best' ? 80 : parseInt(quality)) :
    quality;

  const qualityCompression = Math.max(0.2, Math.min(1, qualityNum / 100));

  // Dimension-based compression (1280x720 vs original)
  const targetPixels = targetDimensions.width * targetDimensions.height;
  const standardPixels = 1280 * 720; // Our standard size
  const dimensionRatio = Math.min(1, targetPixels / standardPixels);

  // Calculate estimated size
  const estimatedBytes = Math.round(
    originalSizeBytes * webpCompression * qualityCompression * dimensionRatio
  );

  const estimatedKB = Math.round(estimatedBytes / 1024);
  const compressionRatio = Math.round(((originalSizeBytes - estimatedBytes) / originalSizeBytes) * 100);

  return {
    estimatedBytes,
    estimatedKB,
    compressionRatio
  };
}
