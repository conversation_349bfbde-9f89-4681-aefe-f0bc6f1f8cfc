import { Metadata } from 'next';
import { getPostBySlug } from '@/lib/utils/database';
import { getAbsoluteImageUrl } from '@/lib/utils/image-utils';
import StructuredData from '@/components/seo/structured-data';

interface Props {
  params: Promise<{ slug: string }>;
  children: React.ReactNode;
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  try {
    const { slug } = await params;
    const postData = await getPostBySlug(slug);

    if (!postData) {
      return {
        title: 'Post Not Found - Wikify',
        description: 'The requested post could not be found.',
      };
    }

    const { post, author } = postData;
    const title = post.post_title;
    const description = post.post_excerpt || 'Read this comprehensive guide on Wikify.';
    const featuredImage = getAbsoluteImageUrl(post.featured_image);
    const authorName = author?.display_name || author?.user_login || 'Wikify Team';
    
    // Get base URL for canonical URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const canonicalUrl = `${baseUrl}/post/${slug}`;

    return {
      title: `${title} - Wikify`,
      description: description,
      keywords: `${title}, wikify, how to guide, tutorial, ${authorName}`,
      authors: [{ name: authorName }],
      creator: authorName,
      publisher: 'Wikify',
      applicationName: 'Wikify',
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: title,
        description: description,
        siteName: 'Wikify',
        type: 'article',
        url: canonicalUrl,
        images: featuredImage ? [
          {
            url: featuredImage,
            width: 1200,
            height: 630,
            alt: title,
          }
        ] : undefined,
        authors: [authorName],
        publishedTime: post.post_date,
        modifiedTime: post.post_modified,
      },
      twitter: {
        card: 'summary_large_image',
        title: title,
        description: description,
        images: featuredImage ? [featuredImage] : undefined,
        creator: `@${author?.user_login || 'wikify'}`,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata for post:', error);
    return {
      title: 'Wikify - How to Guide you can Trust',
      description: 'Your trusted source for comprehensive guides and tutorials.',
    };
  }
}

export default async function PostLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const postData = await getPostBySlug(slug);

  if (!postData) {
    return children;
  }

  const { post, author } = postData;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const featuredImage = getAbsoluteImageUrl(post.featured_image);
  const authorName = author?.display_name || author?.user_login || 'Wikify Team';

  return (
    <>
      {/* Article Structured Data */}
      <StructuredData
        type="article"
        data={{
          title: post.post_title,
          description: post.post_excerpt || 'Read this comprehensive guide on Wikify.',
          url: `${baseUrl}/post/${slug}`,
          image: featuredImage,
          author: {
            name: authorName,
            url: `${baseUrl}/author/${author?.user_nicename}`,
            image: author?.avatar_url
          },
          publishedTime: post.post_date,
          modifiedTime: post.post_modified,
          category: 'Technology', // You can make this dynamic based on post categories
          tags: [] // You can add post tags here
        }}
      />

      {/* Author Structured Data */}
      {author && (
        <StructuredData
          type="author"
          data={{
            name: authorName,
            url: `${baseUrl}/author/${author.user_nicename}`,
            image: author.avatar_url,
            bio: author.description || `${authorName} is a contributor on Wikify.`
          }}
        />
      )}

      {children}
    </>
  );
}
