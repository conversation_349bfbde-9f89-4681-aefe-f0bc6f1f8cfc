'use client';

import { useEffect, useRef } from 'react';

interface UniversalAdRendererProps {
  adCode: string;
  className?: string;
}

/**
 * Universal Ad Renderer
 * Supports all ad networks with responsive sizing
 * Automatically detects and handles different ad types
 */
export default function UniversalAdRenderer({ adCode, className = '' }: UniversalAdRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || !adCode) return;

    const container = containerRef.current;
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Set the ad code
    container.innerHTML = adCode;

    // Process all ads universally
    const processUniversalAd = () => {
      // Find all script elements
      const scripts = container.querySelectorAll('script');
      
      // Process each script
      scripts.forEach((script) => {
        executeScript(script as HTMLScriptElement);
      });

      // Handle special ad network cases
      handleSpecialAdNetworks(container);

      // Make container responsive
      makeContainerResponsive(container);
    };

    // Process the ad after a short delay
    setTimeout(processUniversalAd, 100);

    // Cleanup function
    return () => {
      // Remove any scripts we added to prevent memory leaks
      const addedScripts = document.head.querySelectorAll('script[data-universal-ad]');
      addedScripts.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      });
    };
  }, [adCode]);

  /**
   * Execute a script element
   */
  const executeScript = (script: HTMLScriptElement) => {
    if (script.src) {
      // External script
      const newScript = document.createElement('script');
      let src = script.src;
      
      // Handle protocol-relative URLs
      if (src.startsWith('//')) {
        src = `https:${src}`;
      }
      
      newScript.src = src;
      newScript.async = script.async !== false;
      newScript.defer = script.defer || false;
      newScript.setAttribute('data-universal-ad', 'true');

      // Copy all attributes
      Array.from(script.attributes).forEach(attr => {
        if (attr.name !== 'src') {
          newScript.setAttribute(attr.name, attr.value);
        }
      });

      newScript.onload = () => {
        // Trigger common ad network initialization
        setTimeout(() => {
          triggerAdNetworkCallbacks();
        }, 500);
      };

      newScript.onerror = () => {
        // Try with HTTP if HTTPS failed
        if (src.startsWith('https://')) {
          const httpSrc = src.replace('https:', 'http:');
          const httpScript = document.createElement('script');
          httpScript.src = httpSrc;
          httpScript.async = true;
          httpScript.setAttribute('data-universal-ad', 'true');
          document.head.appendChild(httpScript);
        }
      };

      // Replace the original script
      script.parentNode?.replaceChild(newScript, script);
    } else if (script.textContent || script.innerHTML) {
      // Inline script
      const newScript = document.createElement('script');
      newScript.textContent = script.textContent || script.innerHTML;
      
      // Copy type attribute
      if (script.type) {
        newScript.type = script.type;
      }

      script.parentNode?.replaceChild(newScript, script);
    }
  };

  /**
   * Handle special ad network requirements
   */
  const handleSpecialAdNetworks = (container: HTMLElement) => {
    // Google AdSense
    const adsenseElements = container.querySelectorAll('.adsbygoogle');
    adsenseElements.forEach((element) => {
      const adElement = element as HTMLElement;
      adElement.style.display = 'block';
      adElement.style.width = '100%';
      adElement.style.height = 'auto';
    });

    // Advertica
    const adverticaElements = container.querySelectorAll('ins[data-domain]');
    adverticaElements.forEach((element) => {
      const insElement = element as HTMLElement;
      
      // Get original dimensions
      const dataWidth = insElement.getAttribute('data-width') || '300';
      const dataHeight = insElement.getAttribute('data-height') || '250';
      
      // Make responsive while maintaining aspect ratio
      const width = parseInt(dataWidth);
      const height = parseInt(dataHeight);
      
      if (width > 0 && height > 0) {
        const aspectRatio = (height / width) * 100;
        
        insElement.style.display = 'block';
        insElement.style.width = '100%';
        insElement.style.maxWidth = `${width}px`;
        insElement.style.height = '0';
        insElement.style.paddingBottom = `${aspectRatio}%`;
        insElement.style.position = 'relative';
        insElement.style.overflow = 'hidden';
      } else {
        // Fallback for ads without dimensions
        insElement.style.display = 'block';
        insElement.style.width = '100%';
        insElement.style.minHeight = '250px';
      }
    });

    // Media.net
    const mediaNetElements = container.querySelectorAll('[data-type="ad"]');
    mediaNetElements.forEach((element) => {
      const adElement = element as HTMLElement;
      adElement.style.width = '100%';
      adElement.style.height = 'auto';
    });

    // Amazon Associates
    const amazonElements = container.querySelectorAll('iframe[src*="amazon"]');
    amazonElements.forEach((element) => {
      const iframe = element as HTMLIFrameElement;
      iframe.style.width = '100%';
      iframe.style.height = 'auto';
    });

    // Generic iframe handling
    const iframes = container.querySelectorAll('iframe');
    iframes.forEach((iframe) => {
      const iframeElement = iframe as HTMLIFrameElement;
      
      // Get original dimensions
      const originalWidth = iframeElement.width || iframeElement.getAttribute('width');
      const originalHeight = iframeElement.height || iframeElement.getAttribute('height');
      
      if (originalWidth && originalHeight) {
        const width = parseInt(originalWidth);
        const height = parseInt(originalHeight);
        
        if (width > 0 && height > 0) {
          const aspectRatio = (height / width) * 100;
          
          // Create responsive wrapper
          const wrapper = document.createElement('div');
          wrapper.style.position = 'relative';
          wrapper.style.width = '100%';
          wrapper.style.maxWidth = `${width}px`;
          wrapper.style.height = '0';
          wrapper.style.paddingBottom = `${aspectRatio}%`;
          wrapper.style.overflow = 'hidden';
          
          // Style iframe
          iframeElement.style.position = 'absolute';
          iframeElement.style.top = '0';
          iframeElement.style.left = '0';
          iframeElement.style.width = '100%';
          iframeElement.style.height = '100%';
          iframeElement.style.border = 'none';
          
          // Wrap iframe
          iframeElement.parentNode?.insertBefore(wrapper, iframeElement);
          wrapper.appendChild(iframeElement);
        }
      }
    });
  };

  /**
   * Make container responsive
   */
  const makeContainerResponsive = (container: HTMLElement) => {
    // Apply responsive styles to container
    container.style.width = '100%';
    container.style.maxWidth = '100%';
    container.style.overflow = 'hidden';
    
    // Handle images
    const images = container.querySelectorAll('img');
    images.forEach((img) => {
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
      img.style.display = 'block';
    });

    // Handle divs with fixed widths
    const divs = container.querySelectorAll('div[style*="width"]');
    divs.forEach((div) => {
      const divElement = div as HTMLElement;
      const style = divElement.getAttribute('style') || '';
      
      // Extract width value
      const widthMatch = style.match(/width:\s*(\d+)px/);
      if (widthMatch) {
        const originalWidth = parseInt(widthMatch[1]);
        divElement.style.width = '100%';
        divElement.style.maxWidth = `${originalWidth}px`;
      }
    });
  };

  /**
   * Trigger ad network callbacks
   */
  const triggerAdNetworkCallbacks = () => {
    if (typeof window === 'undefined') return;

    // Google AdSense
    if (window.adsbygoogle) {
      try {
        (window.adsbygoogle as any[]).push({});
      } catch (e) {
        // Ignore errors
      }
    }

    // Trigger DOM events for ad networks that listen for them
    setTimeout(() => {
      window.dispatchEvent(new Event('load'));
      window.dispatchEvent(new Event('DOMContentLoaded'));
      window.dispatchEvent(new Event('resize'));
    }, 100);

    // Look for and call any global ad initialization functions
    const globalKeys = Object.keys(window);
    const adKeys = globalKeys.filter(key => 
      key.toLowerCase().includes('ad') || 
      key.toLowerCase().includes('banner') ||
      key.toLowerCase().includes('responsive')
    );
    
    adKeys.forEach(key => {
      try {
        const func = (window as any)[key];
        if (typeof func === 'function' && key.includes('init')) {
          func();
        }
      } catch (e) {
        // Silently handle errors
      }
    });
  };

  return (
    <div
      ref={containerRef}
      className={`universal-ad-container ${className}`}
      style={{
        width: '100%',
        maxWidth: '100%',
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center'
      }}
    />
  );
}
