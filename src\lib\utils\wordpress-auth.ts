import crypto from 'crypto';
import bcrypt from 'bcryptjs';

/**
 * WordPress password verification
 * WordPress uses a custom password hashing system
 */

export function verifyWordPressPassword(password: string, hash: string): boolean {
  try {
    // Handle bcrypt hashes (modern WordPress installations and our updated format)
    if (hash.startsWith('$2y$') || hash.startsWith('$2a$') || hash.startsWith('$2b$')) {
      return bcrypt.compareSync(password, hash);
    }

    // Handle WordPress custom format with $wp$ prefix
    if (hash.startsWith('$wp$')) {
      // Remove the $wp$ prefix and get the actual hash
      const actualHash = hash.substring(4);

      // Check if it's a bcrypt hash
      if (actualHash.startsWith('2y$') || actualHash.startsWith('2a$') || actualHash.startsWith('2b$')) {
        // Add back the $ prefix for bcrypt
        const bcryptHash = '$' + actualHash;
        return bcrypt.compareSync(password, bcryptHash);
      }

      // If not bcrypt, try MD5
      const md5Hash = crypto.createHash('md5').update(password).digest('hex');
      return md5Hash === actualHash;
    }

    // WordPress uses $P$ prefix for portable PHP password hashing
    if (hash.startsWith('$P$') || hash.startsWith('$H$')) {
      return verifyPortableHash(password, hash);
    }

    // Fallback to MD5 for very old WordPress installations
    if (hash.length === 32) {
      return crypto.createHash('md5').update(password).digest('hex') === hash;
    }

    return false;
  } catch (error) {
    console.error('Password verification error:', error.message);
    return false;
  }
}

function verifyPortableHash(password: string, hash: string): boolean {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  
  if (hash.length !== 34) return false;
  
  const count_log2 = itoa64.indexOf(hash[3]);
  if (count_log2 < 7 || count_log2 > 30) return false;
  
  const count = 1 << count_log2;
  const salt = hash.substring(4, 12);
  
  if (hash.length !== 34) return false;
  
  let hashResult = crypto.createHash('md5').update(salt + password).digest();
  
  for (let i = 0; i < count; i++) {
    hashResult = crypto.createHash('md5').update(Buffer.concat([hashResult, Buffer.from(password)])).digest();
  }
  
  const output = hash.substring(0, 12) + encode64(hashResult, 16);
  
  return output === hash;
}

function encode64(input: Buffer, count: number): string {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let output = '';
  let i = 0;
  
  do {
    let value = input[i++];
    output += itoa64[value & 0x3f];
    
    if (i < count) {
      value |= input[i] << 8;
    }
    
    output += itoa64[(value >> 6) & 0x3f];
    
    if (i++ >= count) break;
    
    if (i < count) {
      value |= input[i] << 16;
    }
    
    output += itoa64[(value >> 12) & 0x3f];
    
    if (i++ >= count) break;
    
    output += itoa64[(value >> 18) & 0x3f];
  } while (i < count);
  
  return output;
}

/**
 * Generate WordPress compatible password hash
 */
export function hashWordPressPassword(password: string): string {
  const itoa64 = './0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const count_log2 = 8; // 2^8 = 256 iterations
  const salt = crypto.randomBytes(6).toString('base64').substring(0, 8);
  
  const count = 1 << count_log2;
  let hash = crypto.createHash('md5').update(salt + password).digest();
  
  for (let i = 0; i < count; i++) {
    hash = crypto.createHash('md5').update(Buffer.concat([hash, Buffer.from(password)])).digest();
  }
  
  return '$P$' + itoa64[count_log2] + salt + encode64(hash, 16);
}
