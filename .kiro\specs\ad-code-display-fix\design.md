# Design Document

## Overview

This design addresses the ad code display functionality in the Wikify blogging platform. The system needs to ensure that ad codes entered in the post editor are properly saved to the database, retrieved when displaying posts, and executed correctly on the frontend. The design focuses on fixing the current implementation where ad codes are not showing up on post view pages despite being entered in the editor.

## Architecture

### Current System Analysis

The current system has the following components:
- **PostForm Component**: Contains ad code input fields (`beforeContentAds`, `afterContentAds`)
- **Post API Endpoints**: Handle saving and retrieving post data including ad codes
- **Database Layer**: Stores ad codes in `postmeta` table with keys `_before_content_ads` and `_after_content_ads`
- **Post View Component**: Displays posts and should render ad codes
- **Permission System**: Controls which users can insert ad codes via `canInsertAds` flag

### Root Cause Analysis

Based on code analysis, the potential issues are:
1. **Permission Check**: Users may not have `canInsertAds` permission set correctly
2. **Data Retrieval**: Ad codes may not be properly retrieved from the database
3. **Script Execution**: JavaScript in ad codes may not be executing properly
4. **API Data Flow**: Ad codes may not be passed through the API correctly

## Components and Interfaces

### 1. User Permission Management

**Component**: `AuthService`
**Interface**: User session management with ad permissions

```typescript
interface UserSession {
  user: {
    id: string;
    role: string;
    canInsertAds?: boolean; // This flag controls ad code access
  }
}
```

**Design Decision**: Implement a permission check system that properly sets the `canInsertAds` flag based on user roles (ADMIN, EDITOR should have access).

### 2. Post Metadata Management

**Component**: `PostMetaService`
**Interface**: Database operations for ad codes

```typescript
interface AdCodeMeta {
  beforeContentAds: string;
  afterContentAds: string;
}

interface PostMetaOperations {
  saveAdCodes(postId: number, adCodes: AdCodeMeta): Promise<void>;
  getAdCodes(postId: number): Promise<AdCodeMeta>;
}
```

**Design Decision**: Ensure ad codes are consistently saved and retrieved using the correct meta keys (`_before_content_ads`, `_after_content_ads`).

### 3. Ad Code Renderer

**Component**: `AdCodeRenderer`
**Interface**: Frontend component for displaying and executing ad codes

```typescript
interface AdCodeRendererProps {
  adCode: string;
  position: 'before' | 'after';
  className?: string;
}
```

**Design Decision**: Create a dedicated component that safely renders ad codes and handles script execution with proper error handling.

### 4. Script Execution Manager

**Component**: `ScriptExecutor`
**Interface**: Handles dynamic script execution for ad codes

```typescript
interface ScriptExecutor {
  executeAdScripts(container: HTMLElement): void;
  executeInlineScript(script: HTMLScriptElement): void;
  executeExternalScript(script: HTMLScriptElement): void;
}
```

**Design Decision**: Implement a robust script execution system that handles both inline and external scripts with proper cleanup and error handling.

## Data Models

### Post Model Extension

```typescript
interface Post {
  ID: number;
  post_title: string;
  post_content: string;
  post_excerpt: string;
  // ... other fields
  beforeContentAds?: string; // Ad code before content
  afterContentAds?: string;  // Ad code after content
}
```

### PostMeta Model

```typescript
interface PostMeta {
  meta_id: number;
  post_id: number;
  meta_key: string; // '_before_content_ads' | '_after_content_ads'
  meta_value: string; // The actual ad code
}
```

## Error Handling

### 1. Permission Errors
- **Scenario**: User without ad permissions tries to save ad codes
- **Handling**: Silently ignore ad codes in API, show appropriate UI state in frontend
- **User Experience**: Hide ad code fields for unauthorized users

### 2. Script Execution Errors
- **Scenario**: Ad code contains invalid JavaScript
- **Handling**: Catch and log errors, prevent page crashes
- **User Experience**: Page continues to function normally

### 3. Database Errors
- **Scenario**: Failed to save/retrieve ad codes
- **Handling**: Log errors, provide fallback behavior
- **User Experience**: Show appropriate error messages

### 4. Network Errors
- **Scenario**: External ad scripts fail to load
- **Handling**: Set timeouts, provide fallback content
- **User Experience**: Page loads normally without ads

## Testing Strategy

### 1. Unit Tests
- **Permission System**: Test user role-based ad permissions
- **Database Operations**: Test ad code save/retrieve operations
- **Script Execution**: Test various ad code formats and script types
- **Error Handling**: Test error scenarios and recovery

### 2. Integration Tests
- **End-to-End Flow**: Test complete flow from editor to display
- **API Integration**: Test API endpoints with ad code data
- **Database Integration**: Test postmeta operations
- **Permission Integration**: Test permission checks across components

### 3. Frontend Tests
- **Component Rendering**: Test ad code display components
- **Script Execution**: Test JavaScript execution in ad codes
- **Responsive Design**: Test ad display on different screen sizes
- **Error States**: Test error handling in UI components

### 4. Security Tests
- **XSS Prevention**: Test protection against malicious scripts
- **Permission Bypass**: Test unauthorized access attempts
- **Input Validation**: Test ad code input sanitization
- **Script Isolation**: Test script execution isolation

## Implementation Approach

### Phase 1: Permission System Fix
1. Implement proper user permission checking for ad insertion
2. Update session management to include `canInsertAds` flag
3. Fix role-based permission assignment (ADMIN, EDITOR get access)

### Phase 2: Database Operations
1. Verify and fix ad code saving in POST/PUT endpoints
2. Ensure proper retrieval of ad codes in getPostBySlug function
3. Add error handling for database operations

### Phase 3: Frontend Display
1. Create dedicated AdCodeRenderer component
2. Implement proper script execution with error handling
3. Update post view component to use new renderer

### Phase 4: Testing and Validation
1. Add comprehensive tests for all components
2. Test with real ad codes (Google AdSense, etc.)
3. Validate responsive design and cross-browser compatibility

## Security Considerations

### 1. Script Execution Safety
- **Approach**: Execute scripts in controlled environment
- **Validation**: Basic validation of ad code content
- **Isolation**: Prevent scripts from accessing sensitive page data

### 2. Permission Enforcement
- **Server-Side**: Always validate permissions on API endpoints
- **Client-Side**: Hide UI elements based on permissions
- **Database**: Ensure only authorized users can save ad codes

### 3. Input Sanitization
- **Ad Code Content**: Basic validation without breaking functionality
- **XSS Prevention**: Rely on browser security for script execution
- **Content Policy**: Implement content security policy if needed

## Performance Considerations

### 1. Script Loading
- **Async Loading**: Load external scripts asynchronously
- **Timeout Handling**: Set reasonable timeouts for script loading
- **Error Recovery**: Graceful handling of failed script loads

### 2. Database Queries
- **Efficient Retrieval**: Fetch ad codes with minimal queries
- **Caching**: Consider caching frequently accessed ad codes
- **Batch Operations**: Optimize bulk operations where possible

### 3. Frontend Performance
- **Lazy Loading**: Load ad scripts only when needed
- **Memory Management**: Proper cleanup of script elements
- **Render Optimization**: Minimize layout shifts from ad loading