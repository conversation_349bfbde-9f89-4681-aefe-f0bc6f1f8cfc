import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserInsertAds } from '@/lib/utils/ads-permissions';

// GET /api/user/ads-permission - Get current user's ads permission
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }

    // Get ads permission
    const canInsertAds = await canUserInsertAds(userId);

    return NextResponse.json({
      success: true,
      data: {
        canInsertAds,
        userId,
        username: session.user.username,
        email: session.user.email
      }
    });

  } catch (error) {
    console.error('Error getting user ads permission:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get ads permission',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
