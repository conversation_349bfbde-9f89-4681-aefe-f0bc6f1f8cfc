import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, usermeta, posts, comments, USER_STATUS } from '@/lib/db/schema';
import { eq, count, and } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { sendApprovalEmail, sendRejectionEmail } from '@/lib/utils/email';

// GET /api/users/[id] - Get single user with stats
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Get user with capabilities
    const result = await db
      .select({
        user: users,
        capabilities: usermeta
      })
      .from(users)
      .leftJoin(usermeta, eq(users.ID, usermeta.user_id))
      .where(eq(users.ID, userId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const { user, capabilities } = result[0];

    // Extract role
    let userRole = 'AUTHOR';
    if (capabilities?.meta_value) {
      const caps = capabilities.meta_value;
      if (caps.includes('administrator')) userRole = 'ADMIN';
      else if (caps.includes('editor')) userRole = 'EDITOR';
      else if (caps.includes('author')) userRole = 'AUTHOR';
    }

    // Get user statistics
    const [postCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(eq(posts.post_author, userId));

    const [commentCount] = await db
      .select({ count: count() })
      .from(comments)
      .where(eq(comments.user_id, userId));

    return NextResponse.json({
      success: true,
      data: {
        id: user.ID,
        username: user.user_login,
        email: user.user_email,
        displayName: user.display_name,
        nicename: user.user_nicename,
        url: user.user_url,
        registered: user.user_registered,
        status: user.user_status,
        role: userRole,
        stats: {
          posts: postCount.count,
          comments: commentCount.count
        }
      }
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);
    const body = await request.json();
    const { username, email, password, displayName, role, url, status } = body;

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const currentUser = existingUser[0];
    const oldStatus = currentUser.user_status;

    // Prepare update data
    const updateData: any = {};

    if (username) updateData.user_login = username;
    if (email) updateData.user_email = email;
    if (displayName !== undefined) updateData.display_name = displayName;
    if (url !== undefined) updateData.user_url = url;
    // Only update status if explicitly provided in request
    if (status !== undefined && status !== null) updateData.user_status = status;

    if (password) {
      updateData.user_pass = await bcrypt.hash(password, 12);
    }

    if (username) {
      updateData.user_nicename = username.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    // Update user
    if (Object.keys(updateData).length > 0) {
      await db
        .update(users)
        .set(updateData)
        .where(eq(users.ID, userId));
    }

    // Send email notification if status changed
    if (status !== undefined && status !== null && status !== oldStatus) {
      try {
        if (status === USER_STATUS.APPROVED && oldStatus !== USER_STATUS.APPROVED) {
          // User was approved
          const emailSent = await sendApprovalEmail(
            currentUser.user_email,
            currentUser.user_login,
            currentUser.display_name,
            false // Default to false, can be updated separately via ads permission API
          );
          if (!emailSent) {
            console.error('Failed to send approval email to:', currentUser.user_email);
          }
        } else if (status === USER_STATUS.REJECTED && oldStatus !== USER_STATUS.REJECTED) {
          // User was rejected
          const emailSent = await sendRejectionEmail(
            currentUser.user_email,
            currentUser.user_login,
            currentUser.display_name
          );
          if (!emailSent) {
            console.error('Failed to send rejection email to:', currentUser.user_email);
          }
        }
      } catch (emailError) {
        console.error('Status change email error:', emailError);
        // Don't fail the update if email fails, just log it
      }
    }

    // Update role if provided
    if (role) {
      let capabilities = '';
      switch (role) {
        case 'ADMIN':
          capabilities = 'a:1:{s:13:"administrator";b:1;}';
          break;
        case 'EDITOR':
          capabilities = 'a:1:{s:6:"editor";b:1;}';
          break;
        case 'AUTHOR':
        default:
          capabilities = 'a:1:{s:6:"author";b:1;}';
          break;
      }

      // Update or insert capabilities
      // First check if capabilities record exists
      const existingCapabilities = await db
        .select()
        .from(usermeta)
        .where(and(
          eq(usermeta.user_id, userId),
          eq(usermeta.meta_key, 'wikify1h_capabilities')
        ))
        .limit(1);

      if (existingCapabilities.length > 0) {
        // Update existing capabilities
        await db
          .update(usermeta)
          .set({ meta_value: capabilities })
          .where(and(
            eq(usermeta.user_id, userId),
            eq(usermeta.meta_key, 'wikify1h_capabilities')
          ));
      } else {
        // Insert new capabilities record
        await db
          .insert(usermeta)
          .values({
            user_id: userId,
            meta_key: 'wikify1h_capabilities',
            meta_value: capabilities
          });
      }
    }

    return NextResponse.json({
      success: true,
      data: { id: userId, ...updateData, role }
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    if (parseInt(session.user.id) === userId) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Delete user metadata first
    await db
      .delete(usermeta)
      .where(eq(usermeta.user_id, userId));

    // Delete user
    await db
      .delete(users)
      .where(eq(users.ID, userId));

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
