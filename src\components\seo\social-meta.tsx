import { Metadata } from 'next';

interface SocialMetaProps {
  title: string;
  description: string;
  url: string;
  image?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
  siteName?: string;
}

export function generateSocialMeta({
  title,
  description,
  url,
  image,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  tags,
  siteName = 'Wikify'
}: SocialMetaProps): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz';
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;
  const imageUrl = image?.startsWith('http') ? image : image ? `${baseUrl}${image}` : `${baseUrl}/og-default.jpg`;

  return {
    title,
    description,
    keywords: tags?.join(', '),
    authors: author ? [{ name: author }] : undefined,
    creator: author,
    publisher: siteName,
    
    // Canonical URL
    alternates: {
      canonical: fullUrl,
    },

    // Open Graph
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName,
      type,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
        {
          url: imageUrl,
          width: 800,
          height: 600,
          alt: title,
        }
      ],
      locale: 'en_US',
      ...(type === 'article' && {
        authors: author ? [author] : undefined,
        publishedTime,
        modifiedTime,
        section: 'Technology',
        tags,
      }),
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: author ? `@${author.toLowerCase().replace(/\s+/g, '')}` : '@wikify',
      site: '@wikify',
    },

    // Additional meta tags
    other: {
      // Facebook specific
      'fb:app_id': process.env.FACEBOOK_APP_ID || '',
      
      // Article specific
      ...(type === 'article' && publishedTime && {
        'article:published_time': publishedTime,
        'article:modified_time': modifiedTime || publishedTime,
        'article:author': author,
        'article:section': 'Technology',
        'article:tag': tags?.join(','),
      }),

      // Additional SEO tags
      'theme-color': '#3b82f6',
      'msapplication-TileColor': '#3b82f6',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'format-detection': 'telephone=no',
    },

    // Robots
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // Verification tags (add your verification codes)
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION || '',
      yandex: process.env.YANDEX_VERIFICATION || '',
      yahoo: process.env.YAHOO_VERIFICATION || '',
      other: {
        'msvalidate.01': process.env.BING_VERIFICATION || '',
        'p:domain_verify': process.env.PINTEREST_VERIFICATION || '',
      },
    },
  };
}

// Helper function for generating image URLs
export function getOptimizedImageUrl(originalUrl?: string, width = 1200, height = 630): string {
  if (!originalUrl) {
    return `${process.env.NEXT_PUBLIC_BASE_URL}/og-default.jpg`;
  }

  // If it's already an absolute URL, return as is
  if (originalUrl.startsWith('http')) {
    return originalUrl;
  }

  // For local images, you might want to add optimization parameters
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.wikify.xyz';
  return `${baseUrl}${originalUrl}`;
}

// Helper function for generating article tags
export function generateArticleTags(title: string, category?: string, customTags?: string[]): string[] {
  const baseTags = ['wikify', 'tutorial', 'guide', 'how-to'];
  const titleWords = title.toLowerCase().split(' ').filter(word => word.length > 3);
  
  return [
    ...baseTags,
    ...(category ? [category.toLowerCase()] : []),
    ...titleWords.slice(0, 5), // First 5 meaningful words from title
    ...(customTags || [])
  ].filter((tag, index, self) => self.indexOf(tag) === index); // Remove duplicates
}
