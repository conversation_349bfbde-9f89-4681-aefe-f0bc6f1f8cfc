import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, usermeta } from '@/lib/db/schema';
import { eq, and, ne } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

// GET /api/profile - Get current user's profile
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get user data
    const userData = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!userData.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const user = userData[0];

    // Get user bio from usermeta
    const bioData = await db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, userId));

    const bio = bioData.find(meta => meta.meta_key === 'description')?.meta_value || '';

    return NextResponse.json({
      success: true,
      data: {
        id: user.ID,
        username: user.user_login,
        email: user.user_email,
        displayName: user.display_name,
        bio: bio,
        url: user.user_url,
        registered: user.user_registered,
        role: session.user.role
      }
    });

  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch profile',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/profile - Update current user's profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await request.json();
    const { displayName, email, bio, url, currentPassword, newPassword } = body;

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const user = existingUser[0];

    // If changing password, verify current password
    if (newPassword) {
      if (!currentPassword) {
        return NextResponse.json(
          { success: false, error: 'Current password is required to change password' },
          { status: 400 }
        );
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.user_pass);
      if (!isCurrentPasswordValid) {
        return NextResponse.json(
          { success: false, error: 'Current password is incorrect' },
          { status: 400 }
        );
      }

      if (newPassword.length < 6) {
        return NextResponse.json(
          { success: false, error: 'New password must be at least 6 characters long' },
          { status: 400 }
        );
      }
    }

    // Validate email if provided
    if (email !== undefined && email !== user.user_email) {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { success: false, error: 'Please enter a valid email address' },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const emailExists = await db
        .select()
        .from(users)
        .where(and(
          eq(users.user_email, email.toLowerCase()),
          ne(users.ID, userId)
        ))
        .limit(1);

      if (emailExists.length > 0) {
        return NextResponse.json(
          { success: false, error: 'This email address is already in use by another account' },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};

    if (displayName !== undefined) updateData.display_name = displayName;
    if (email !== undefined) updateData.user_email = email.toLowerCase();
    if (url !== undefined) updateData.user_url = url;

    if (newPassword) {
      updateData.user_pass = await bcrypt.hash(newPassword, 12);
    }

    // Update user
    if (Object.keys(updateData).length > 0) {
      await db
        .update(users)
        .set(updateData)
        .where(eq(users.ID, userId));
    }

    // Update bio in usermeta
    if (bio !== undefined) {
      // Check if bio meta exists
      const existingBio = await db
        .select()
        .from(usermeta)
        .where(eq(usermeta.user_id, userId));

      const bioMeta = existingBio.find(meta => meta.meta_key === 'description');

      if (bioMeta) {
        // Update existing bio
        await db
          .update(usermeta)
          .set({ meta_value: bio })
          .where(and(
            eq(usermeta.user_id, userId),
            eq(usermeta.meta_key, 'description')
          ));
      } else {
        // Insert new bio
        await db.insert(usermeta).values({
          user_id: userId,
          meta_key: 'description',
          meta_value: bio
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: newPassword ? 'Profile and password updated successfully' : 'Profile updated successfully',
      data: {
        id: userId,
        displayName: displayName || user.display_name,
        email: email || user.user_email,
        bio: bio || '',
        url: url || user.user_url
      }
    });

  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update profile',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
