import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, posts, terms, term_taxonomy } from '@/lib/db/schema';
import { eq, count, desc, and, gte, sql } from 'drizzle-orm';

// GET /api/admin/stats - Get comprehensive admin dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get basic counts
    const [userCount] = await db.select({ count: count() }).from(users);
    const [postCount] = await db.select({ count: count() }).from(posts).where(eq(posts.post_type, 'post'));
    const [publishedPostCount] = await db.select({ count: count() }).from(posts).where(and(eq(posts.post_type, 'post'), eq(posts.post_status, 'publish')));
    const [draftPostCount] = await db.select({ count: count() }).from(posts).where(and(eq(posts.post_type, 'post'), eq(posts.post_status, 'draft')));

    const [categoryCount] = await db.select({ count: count() }).from(term_taxonomy).where(eq(term_taxonomy.taxonomy, 'category'));
    const [tagCount] = await db.select({ count: count() }).from(term_taxonomy).where(eq(term_taxonomy.taxonomy, 'post_tag'));

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [recentUsers] = await db.select({ count: count() }).from(users).where(gte(users.user_registered, thirtyDaysAgo));
    const [recentPosts] = await db.select({ count: count() }).from(posts).where(and(eq(posts.post_type, 'post'), gte(posts.post_date, thirtyDaysAgo)));


    // Get top authors by post count
    const topAuthors = await db
      .select({
        author: users,
        postCount: count(posts.ID)
      })
      .from(users)
      .leftJoin(posts, eq(users.ID, posts.post_author))
      .where(eq(posts.post_type, 'post'))
      .groupBy(users.ID)
      .orderBy(desc(count(posts.ID)))
      .limit(5);

    // Get recent posts
    const recentPostsList = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(eq(posts.post_type, 'post'))
      .orderBy(desc(posts.post_date))
      .limit(5);



    // Get user role distribution
    const userRoles = await db
      .select({
        role: sql<string>`CASE 
          WHEN ${users.ID} IN (
            SELECT user_id FROM wikify1h_usermeta 
            WHERE meta_key = 'wikify1h_capabilities' 
            AND meta_value LIKE '%administrator%'
          ) THEN 'ADMIN'
          WHEN ${users.ID} IN (
            SELECT user_id FROM wikify1h_usermeta 
            WHERE meta_key = 'wikify1h_capabilities' 
            AND meta_value LIKE '%editor%'
          ) THEN 'EDITOR'
          ELSE 'AUTHOR'
        END`,
        count: count()
      })
      .from(users)
      .groupBy(sql`CASE 
        WHEN ${users.ID} IN (
          SELECT user_id FROM wikify1h_usermeta 
          WHERE meta_key = 'wikify1h_capabilities' 
          AND meta_value LIKE '%administrator%'
        ) THEN 'ADMIN'
        WHEN ${users.ID} IN (
          SELECT user_id FROM wikify1h_usermeta 
          WHERE meta_key = 'wikify1h_capabilities' 
          AND meta_value LIKE '%editor%'
        ) THEN 'EDITOR'
        ELSE 'AUTHOR'
      END`);

    // Get post status distribution
    const postStatuses = await db
      .select({
        status: posts.post_status,
        count: count()
      })
      .from(posts)
      .where(eq(posts.post_type, 'post'))
      .groupBy(posts.post_status);

    // Get monthly post creation stats (last 12 months)
    const monthlyStats = await db
      .select({
        month: sql<string>`DATE_FORMAT(${posts.post_date}, '%Y-%m')`,
        count: count()
      })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        gte(posts.post_date, sql`DATE_SUB(NOW(), INTERVAL 12 MONTH)`)
      ))
      .groupBy(sql`DATE_FORMAT(${posts.post_date}, '%Y-%m')`)
      .orderBy(sql`DATE_FORMAT(${posts.post_date}, '%Y-%m')`);

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalUsers: userCount.count,
          totalPosts: postCount.count,
          publishedPosts: publishedPostCount.count,
          draftPosts: draftPostCount.count,
          totalCategories: categoryCount.count,
          totalTags: tagCount.count
        },
        recentActivity: {
          newUsers: recentUsers.count,
          newPosts: recentPosts.count
        },
        topAuthors: <AUTHORS>
          id: author.ID,
          name: author.display_name || author.user_login,
          email: author.user_email,
          postCount
        })),
        recentPosts: recentPostsList.map(({ post, author }) => ({
          id: post.ID,
          title: post.post_title,
          status: post.post_status,
          date: post.post_date,
          author: author ? {
            id: author.ID,
            username: author.user_login,
            displayName: author.display_name || author.user_login,
            email: author.user_email
          } : null
        })),

        userRoles: userRoles.map(({ role, count }) => ({
          role,
          count
        })),
        postStatuses: postStatuses.map(({ status, count }) => ({
          status,
          count
        })),
        monthlyStats: monthlyStats.map(({ month, count }) => ({
          month,
          count
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch admin statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
