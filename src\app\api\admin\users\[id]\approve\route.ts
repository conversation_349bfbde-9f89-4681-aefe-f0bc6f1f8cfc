import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, USER_STATUS } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { sendApprovalEmail, sendRejectionEmail } from '@/lib/utils/email';

// PUT /api/admin/users/[id]/approve - Approve user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Parse request body for ads permission
    const body = await request.json().catch(() => ({}));
    const { canInsertAds = false } = body;

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update user status to approved
    await db
      .update(users)
      .set({ user_status: USER_STATUS.APPROVED })
      .where(eq(users.ID, userId));

    // Set ads permission if specified
    if (typeof canInsertAds === 'boolean') {
      const { setAdsPermission } = await import('@/lib/utils/ads-permissions');
      await setAdsPermission(userId, canInsertAds);
    }

    // Send approval email to the user
    try {
      const emailSent = await sendApprovalEmail(
        existingUser[0].user_email,
        existingUser[0].user_login,
        existingUser[0].display_name,
        canInsertAds
      );
      if (!emailSent) {
        console.error('Failed to send approval email to:', existingUser[0].user_email);
        // Don't fail the approval if email fails, just log it
      }
    } catch (emailError) {
      console.error('Approval email error:', emailError);
      // Don't fail the approval if email fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'User approved successfully and notification email sent',
      data: {
        id: userId,
        status: 'approved',
        canInsertAds,
        username: existingUser[0].user_login,
        email: existingUser[0].user_email
      }
    });

  } catch (error) {
    console.error('Error approving user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to approve user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id]/approve - Reject user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update user status to rejected
    await db
      .update(users)
      .set({ user_status: USER_STATUS.REJECTED })
      .where(eq(users.ID, userId));

    // Send rejection email to the user
    try {
      const emailSent = await sendRejectionEmail(
        existingUser[0].user_email,
        existingUser[0].user_login,
        existingUser[0].display_name
      );
      if (!emailSent) {
        console.error('Failed to send rejection email to:', existingUser[0].user_email);
        // Don't fail the rejection if email fails, just log it
      }
    } catch (emailError) {
      console.error('Rejection email error:', emailError);
      // Don't fail the rejection if email fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'User rejected successfully and notification email sent',
      data: {
        id: userId,
        status: 'rejected'
      }
    });

  } catch (error) {
    console.error('Error rejecting user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to reject user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
