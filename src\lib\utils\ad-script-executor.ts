/**
 * Ad Script Executor Utility
 * Handles the execution of advertisement scripts in a safe and reliable manner
 */

export interface AdScriptExecutorOptions {
  timeout?: number;
  retryAttempts?: number;
  debug?: boolean;
}

export class AdScriptExecutor {
  private options: Required<AdScriptExecutorOptions>;
  private executedScripts: Set<string> = new Set();

  constructor(options: AdScriptExecutorOptions = {}) {
    this.options = {
      timeout: 10000, // 10 seconds
      retryAttempts: 2,
      debug: false,
      ...options
    };
  }

  /**
   * Execute all scripts within ad containers
   */
  public executeAdScripts(containerSelector: string = '.ad-container'): void {
    const containers = document.querySelectorAll(containerSelector);
    
    containers.forEach((container, index) => {
      this.log(`Processing ad container ${index + 1}/${containers.length}`);
      this.executeScriptsInContainer(container as HTMLElement);
    });
  }

  /**
   * Execute scripts within a specific container
   */
  private executeScriptsInContainer(container: HTMLElement): void {
    const scripts = container.querySelectorAll('script');
    
    scripts.forEach((script, index) => {
      this.log(`Processing script ${index + 1}/${scripts.length} in container`);
      this.executeScript(script as HTMLScriptElement);
    });

    // Trigger ad network callbacks after script execution
    this.triggerAdNetworkCallbacks();
  }

  /**
   * Execute a single script element
   */
  private executeScript(script: HTMLScriptElement): void {
    try {
      if (script.src) {
        this.executeExternalScript(script);
      } else if (script.textContent || script.innerHTML) {
        this.executeInlineScript(script);
      }
    } catch (error) {
      console.warn('Error executing ad script:', error);
    }
  }

  /**
   * Execute external script
   */
  private executeExternalScript(script: HTMLScriptElement): void {
    const src = script.src;
    
    // Avoid executing the same script multiple times
    if (this.executedScripts.has(src)) {
      this.log(`Script already executed: ${src}`);
      return;
    }

    const newScript = document.createElement('script');
    newScript.src = src;
    newScript.async = script.async !== false; // Default to async
    newScript.defer = script.defer || false;

    // Copy attributes
    this.copyScriptAttributes(script, newScript);

    // Add timeout handling
    const timeoutId = setTimeout(() => {
      console.warn(`Script timeout: ${src}`);
      newScript.remove();
    }, this.options.timeout);

    // Success handler
    newScript.onload = () => {
      clearTimeout(timeoutId);
      this.executedScripts.add(src);
      this.log(`Successfully loaded script: ${src}`);
    };

    // Error handler
    newScript.onerror = (error) => {
      clearTimeout(timeoutId);
      console.warn(`Failed to load script: ${src}`, error);
    };

    // Replace the original script
    script.parentNode?.replaceChild(newScript, script);
  }

  /**
   * Execute inline script
   */
  private executeInlineScript(script: HTMLScriptElement): void {
    const content = script.textContent || script.innerHTML;
    
    if (!content.trim()) {
      return;
    }

    const newScript = document.createElement('script');
    newScript.textContent = content;

    // Copy type attribute
    if (script.type) {
      newScript.type = script.type;
    }

    // Replace the original script
    script.parentNode?.replaceChild(newScript, script);
    
    this.log('Executed inline script');
  }

  /**
   * Copy relevant attributes from old script to new script
   */
  private copyScriptAttributes(oldScript: HTMLScriptElement, newScript: HTMLScriptElement): void {
    const attributesToCopy = ['type', 'charset', 'crossorigin', 'integrity', 'referrerpolicy'];
    
    attributesToCopy.forEach(attr => {
      const value = oldScript.getAttribute(attr);
      if (value) {
        newScript.setAttribute(attr, value);
      }
    });

    // Copy data attributes
    Array.from(oldScript.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        newScript.setAttribute(attr.name, attr.value);
      }
    });
  }

  /**
   * Trigger common ad network callbacks
   */
  private triggerAdNetworkCallbacks(): void {
    if (typeof window === 'undefined') return;

    // Google AdSense
    if (window.adsbygoogle) {
      try {
        (window.adsbygoogle as any[]).push({});
        this.log('Triggered AdSense callback');
      } catch (error) {
        // Ignore AdSense errors for non-AdSense ads
      }
    }

    // Other ad networks can be added here
    // Example: Media.net, Amazon, etc.
  }

  /**
   * Log debug messages
   */
  private log(message: string): void {
    if (this.options.debug) {
      console.log(`[AdScriptExecutor] ${message}`);
    }
  }

  /**
   * Clear executed scripts cache
   */
  public clearCache(): void {
    this.executedScripts.clear();
  }
}

/**
 * Global instance for easy access
 */
export const adScriptExecutor = new AdScriptExecutor({
  debug: process.env.NODE_ENV === 'development'
});

/**
 * Convenience function to execute ad scripts
 */
export function executeAdScripts(containerSelector?: string): void {
  adScriptExecutor.executeAdScripts(containerSelector);
}

/**
 * Enhanced script execution with retry logic
 */
export function executeAdScriptsWithRetry(
  containerSelector: string = '.ad-container',
  maxRetries: number = 2,
  delay: number = 1000
): void {
  let attempts = 0;

  const tryExecute = () => {
    attempts++;
    
    try {
      executeAdScripts(containerSelector);
    } catch (error) {
      console.warn(`Ad script execution attempt ${attempts} failed:`, error);
      
      if (attempts < maxRetries) {
        setTimeout(tryExecute, delay * attempts);
      } else {
        console.error('All ad script execution attempts failed');
      }
    }
  };

  tryExecute();
}
