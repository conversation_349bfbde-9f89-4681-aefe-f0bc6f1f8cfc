import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { comments, users, posts } from '@/lib/db/schema';
import { eq, desc, and, count } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('post_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'approved';
    
    const offset = (page - 1) * limit;

    if (!postId) {
      return NextResponse.json(
        { success: false, error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // Get comments for the post
    let query = db
      .select({
        comment: comments,
        author: users
      })
      .from(comments)
      .leftJoin(users, eq(comments.user_id, users.ID))
      .where(eq(comments.comment_post_ID, parseInt(postId)));

    // Filter by approval status
    if (status === 'approved') {
      query = query.where(eq(comments.comment_approved, '1'));
    } else if (status === 'pending') {
      query = query.where(eq(comments.comment_approved, '0'));
    }

    const result = await query
      .orderBy(desc(comments.comment_date))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(comments)
      .where(and(
        eq(comments.comment_post_ID, parseInt(postId)),
        status === 'approved' ? eq(comments.comment_approved, '1') : undefined
      ).filter(Boolean));

    // Format comments with threading
    const formattedComments = result.map(({ comment, author }) => ({
      id: comment.comment_ID,
      content: comment.comment_content,
      date: comment.comment_date,
      approved: comment.comment_approved === '1',
      parentId: comment.comment_parent || null,
      author: author ? {
        id: author.ID,
        username: author.user_login,
        displayName: author.display_name,
        email: author.user_email
      } : {
        name: comment.comment_author,
        email: comment.comment_author_email,
        url: comment.comment_author_url
      }
    }));

    return NextResponse.json({
      success: true,
      data: formattedComments,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: offset + limit < totalCount
      }
    });

  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch comments',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const body = await request.json();
    const { postId, content, parentId, authorName, authorEmail } = body;

    if (!postId || !content) {
      return NextResponse.json(
        { success: false, error: 'Post ID and content are required' },
        { status: 400 }
      );
    }

    // Verify post exists
    const post = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, parseInt(postId)))
      .limit(1);

    if (!post.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if comments are allowed
    if (post[0].comment_status !== 'open') {
      return NextResponse.json(
        { success: false, error: 'Comments are not allowed on this post' },
        { status: 403 }
      );
    }

    // Prepare comment data
    const commentData = {
      comment_post_ID: parseInt(postId),
      comment_content: content,
      comment_date: new Date().toISOString().slice(0, 19).replace('T', ' '),
      comment_date_gmt: new Date().toISOString().slice(0, 19).replace('T', ' '),
      comment_approved: '1', // Auto-approve for now, can add moderation later
      comment_agent: '',
      comment_type: 'comment',
      comment_parent: parentId ? parseInt(parentId) : 0,
      comment_karma: 0,
      user_id: session?.user ? parseInt(session.user.id) : 0,
      comment_author: session?.user ? (session.user.name || session.user.username) : (authorName || ''),
      comment_author_email: session?.user ? session.user.email : (authorEmail || ''),
      comment_author_url: '',
      comment_author_IP: '', // Could extract from request headers if needed
    };

    // Insert comment
    const result = await db.insert(comments).values(commentData);

    // Update post comment count
    await db
      .update(posts)
      .set({ 
        comment_count: post[0].comment_count + 1 
      })
      .where(eq(posts.ID, parseInt(postId)));

    return NextResponse.json({
      success: true,
      data: {
        id: result.insertId,
        content,
        approved: true,
        author: session?.user ? {
          id: parseInt(session.user.id),
          username: session.user.username,
          displayName: session.user.name || session.user.username,
          email: session.user.email
        } : {
          name: authorName,
          email: authorEmail
        }
      }
    });

  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create comment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
