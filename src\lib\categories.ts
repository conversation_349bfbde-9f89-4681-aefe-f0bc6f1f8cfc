import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships, posts } from '@/lib/db/schema';
import { eq, desc, count, and } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils/slug';

export interface Category {
  id: number;
  term_id: number;
  name: string;
  slug: string;
  description: string;
  count: number;
  parent: number;
}

export interface PostCategory {
  post_id: number;
  category_id: number;
  category_name: string;
  category_slug: string;
}

// Get all categories
export async function getCategories(includeEmpty: boolean = false): Promise<Category[]> {
  try {
    let query = db
      .select({
        id: term_taxonomy.term_taxonomy_id,
        term_id: terms.term_id,
        name: terms.name,
        slug: terms.slug,
        description: term_taxonomy.description,
        count: term_taxonomy.count,
        parent: term_taxonomy.parent
      })
      .from(term_taxonomy)
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .orderBy(desc(term_taxonomy.count), terms.name);

    const categories = await query;

    // Filter out empty categories if requested
    return includeEmpty 
      ? categories 
      : categories.filter(cat => cat.count > 0);

  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return [];
  }
}

// Get category by slug
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const result = await db
      .select({
        id: term_taxonomy.term_taxonomy_id,
        term_id: terms.term_id,
        name: terms.name,
        slug: terms.slug,
        description: term_taxonomy.description,
        count: term_taxonomy.count,
        parent: term_taxonomy.parent
      })
      .from(term_taxonomy)
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(and(
        eq(term_taxonomy.taxonomy, 'category'),
        eq(terms.slug, slug)
      ))
      .limit(1);

    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('Failed to fetch category by slug:', error);
    return null;
  }
}

// Get categories for a specific post
export async function getPostCategories(postId: number): Promise<Category[]> {
  try {
    const categories = await db
      .select({
        id: term_taxonomy.term_taxonomy_id,
        term_id: terms.term_id,
        name: terms.name,
        slug: terms.slug,
        description: term_taxonomy.description,
        count: term_taxonomy.count,
        parent: term_taxonomy.parent
      })
      .from(term_relationships)
      .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(and(
        eq(term_relationships.object_id, postId),
        eq(term_taxonomy.taxonomy, 'category')
      ));

    return categories;
  } catch (error) {
    console.error('Failed to fetch post categories:', error);
    return [];
  }
}

// Get posts by category
export async function getPostsByCategory(categorySlug: string, limit: number = 10, offset: number = 0) {
  try {
    const postsInCategory = await db
      .select({
        post_id: posts.ID,
        post_title: posts.post_title,
        post_name: posts.post_name,
        post_excerpt: posts.post_excerpt,
        post_date: posts.post_date,
        post_status: posts.post_status,
        category_name: terms.name,
        category_slug: terms.slug
      })
      .from(posts)
      .leftJoin(term_relationships, eq(posts.ID, term_relationships.object_id))
      .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(and(
        eq(terms.slug, categorySlug),
        eq(term_taxonomy.taxonomy, 'category'),
        eq(posts.post_status, 'publish'),
        eq(posts.post_type, 'post')
      ))
      .orderBy(desc(posts.post_date))
      .limit(limit)
      .offset(offset);

    return postsInCategory;
  } catch (error) {
    console.error('Failed to fetch posts by category:', error);
    return [];
  }
}

// Create a new category
export async function createCategory(name: string, slug?: string, description?: string, parent: number = 0): Promise<Category | null> {
  try {
    // Generate slug if not provided
    const categorySlug = slug || generateSlug(name);

    // Check if category already exists
    const existingCategory = await getCategoryBySlug(categorySlug);
    if (existingCategory) {
      throw new Error('Category with this slug already exists');
    }

    // Create term
    const [newTerm] = await db
      .insert(terms)
      .values({
        name,
        slug: categorySlug,
        term_group: 0
      });

    // Create taxonomy entry
    const [newTaxonomy] = await db
      .insert(term_taxonomy)
      .values({
        term_id: newTerm.insertId,
        taxonomy: 'category',
        description: description || '',
        parent,
        count: 0
      });

    return {
      id: newTaxonomy.insertId,
      term_id: newTerm.insertId,
      name,
      slug: categorySlug,
      description: description || '',
      count: 0,
      parent
    };

  } catch (error) {
    console.error('Failed to create category:', error);
    return null;
  }
}

// Assign category to post
export async function assignCategoryToPost(postId: number, categoryId: number): Promise<boolean> {
  try {
    // Check if relationship already exists
    const existingRelation = await db
      .select()
      .from(term_relationships)
      .where(and(
        eq(term_relationships.object_id, postId),
        eq(term_relationships.term_taxonomy_id, categoryId)
      ))
      .limit(1);

    if (existingRelation.length === 0) {
      // Create new relationship
      await db
        .insert(term_relationships)
        .values({
          object_id: postId,
          term_taxonomy_id: categoryId,
          term_order: 0
        });

      // Update category count
      await db
        .update(term_taxonomy)
        .set({
          count: count() + 1
        })
        .where(eq(term_taxonomy.term_taxonomy_id, categoryId));
    }

    return true;
  } catch (error) {
    console.error('Failed to assign category to post:', error);
    return false;
  }
}
