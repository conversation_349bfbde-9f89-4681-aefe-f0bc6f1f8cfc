import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { options, posts, users, terms, term_taxonomy } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    // Get all settings
    const settingsData = await db
      .select()
      .from(options);

    // Get basic site stats for backup info
    const postsCount = await db
      .select({ count: posts.ID })
      .from(posts);

    const usersCount = await db
      .select({ count: users.ID })
      .from(users);

    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      settings: settingsData,
      stats: {
        posts: postsCount.length,
        users: usersCount.length,
      }
    };

    return NextResponse.json({
      success: true,
      data: backup
    });

  } catch (error) {
    console.error('Backup creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create backup' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { settings } = body;

    if (!settings || !Array.isArray(settings)) {
      return NextResponse.json(
        { error: 'Invalid backup data' },
        { status: 400 }
      );
    }

    // Restore settings
    for (const setting of settings) {
      const existingSetting = await db
        .select()
        .from(options)
        .where(eq(options.option_name, setting.option_name));

      if (existingSetting.length > 0) {
        // Update existing setting
        await db
          .update(options)
          .set({
            option_value: setting.option_value,
            autoload: setting.autoload
          })
          .where(eq(options.option_name, setting.option_name));
      } else {
        // Insert new setting
        await db
          .insert(options)
          .values({
            option_name: setting.option_name,
            option_value: setting.option_value,
            autoload: setting.autoload
          });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Settings restored successfully'
    });

  } catch (error) {
    console.error('Backup restore error:', error);
    return NextResponse.json(
      { error: 'Failed to restore backup' },
      { status: 500 }
    );
  }
}
