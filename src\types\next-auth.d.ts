import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      username: string;
      role: string;
      status: number;
      canInsertAds: boolean;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    username: string;
    role: string;
    status: number;
    canInsertAds: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string;
    username: string;
    status: number;
    canInsertAds: boolean;
  }
}
