/**
 * Test utilities for image optimization functionality
 */

import { optimizeExistingImage, extractPublicId, getOptimizedImageUrl } from '@/lib/cloudinary';

/**
 * Test image optimization with different presets
 */
export function testOptimizationPresets() {
  const testImageUrl = 'https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/test-image.jpg';
  
  console.log('Testing optimization presets:');
  console.log('Original URL:', testImageUrl);
  console.log('---');
  
  // Test different optimization options
  const optimizations = [
    {
      name: 'Blog Post (1280x720 WebP, 30-100KB)',
      options: { width: 1280, height: 720, format: 'webp', quality: '40' }
    },
    {
      name: 'Thumbnail (300x200 WebP)',
      options: { width: 300, height: 200, format: 'webp', quality: 'auto:good' }
    },
    {
      name: 'Avatar (400x400 WebP)',
      options: { width: 400, height: 400, format: 'webp', quality: 'auto:good', crop: 'fill' }
    },
    {
      name: 'High Quality (1200x630 WebP Best)',
      options: { width: 1200, height: 630, format: 'webp', quality: 'auto:best' }
    },
    {
      name: 'Mobile (480x252 WebP)',
      options: { width: 480, height: 252, format: 'webp', quality: 'auto:good' }
    }
  ];
  
  optimizations.forEach(opt => {
    const optimizedUrl = optimizeExistingImage(testImageUrl, opt.options);
    console.log(`${opt.name}:`);
    console.log(`  URL: ${optimizedUrl}`);
    console.log('');
  });
}

/**
 * Test public ID extraction from various URL formats
 */
export function testPublicIdExtraction() {
  const testUrls = [
    'https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/image.jpg',
    'https://res.cloudinary.com/demo/image/upload/v1234567890/wikify-blog/user123/image.png',
    'https://res.cloudinary.com/demo/image/upload/c_fill,w_1200,h_630/wikify-blog/user123/image.webp',
    'https://res.cloudinary.com/demo/image/upload/f_auto,q_auto/wikify-blog/user123/image.gif',
    'wikify-blog/user123/direct-public-id',
    'https://example.com/regular-image.jpg', // Should return null
  ];
  
  console.log('Testing public ID extraction:');
  testUrls.forEach(url => {
    const publicId = extractPublicId(url);
    console.log(`URL: ${url}`);
    console.log(`Public ID: ${publicId || 'null'}`);
    console.log('---');
  });
}

/**
 * Test responsive image generation
 */
export function testResponsiveImages() {
  const publicId = 'wikify-blog/user123/test-image';
  
  console.log('Testing responsive image generation:');
  console.log('Public ID:', publicId);
  console.log('---');
  
  const sizes = [
    { name: 'Mobile', width: 480, height: 252 },
    { name: 'Tablet', width: 768, height: 403 },
    { name: 'Desktop', width: 1200, height: 630 },
    { name: 'Retina', width: 2400, height: 1260 },
  ];
  
  sizes.forEach(size => {
    const url = getOptimizedImageUrl(publicId, {
      width: size.width,
      height: size.height,
      format: 'webp',
      quality: 'auto:good'
    });
    console.log(`${size.name} (${size.width}x${size.height}):`);
    console.log(`  ${url}`);
    console.log('');
  });
}

/**
 * Simulate upload optimization process
 */
export function simulateUploadOptimization(
  fileName: string,
  originalSize: number,
  preset: 'blog-post' | 'featured-image' | 'thumbnail' | 'avatar' | 'custom'
) {
  console.log('Simulating upload optimization:');
  console.log(`File: ${fileName}`);
  console.log(`Original size: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`Preset: ${preset}`);
  console.log('---');
  
  // Simulate optimization settings based on preset
  const presetSettings = {
    'blog-post': { width: 1200, height: 630, quality: 'auto:good', estimatedCompression: 30 },
    'featured-image': { width: 1200, height: 630, quality: 'auto:best', estimatedCompression: 25 },
    'thumbnail': { width: 300, height: 200, quality: 'auto:good', estimatedCompression: 40 },
    'avatar': { width: 400, height: 400, quality: 'auto:good', estimatedCompression: 35 },
    'custom': { width: 1200, height: 630, quality: 'auto:good', estimatedCompression: 30 },
  };
  
  const settings = presetSettings[preset];
  const optimizedSize = originalSize * (1 - settings.estimatedCompression / 100);
  const compressionRatio = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);
  
  console.log('Optimization Results:');
  console.log(`  Dimensions: ${settings.width}x${settings.height}`);
  console.log(`  Format: WebP`);
  console.log(`  Quality: ${settings.quality}`);
  console.log(`  Original size: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`  Optimized size: ${(optimizedSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`  Compression: ${compressionRatio}% smaller`);
  console.log(`  Bandwidth saved: ${((originalSize - optimizedSize) / 1024 / 1024).toFixed(2)} MB`);
  
  return {
    original_size: originalSize,
    optimized_size: optimizedSize,
    compression_ratio: compressionRatio,
    bandwidth_saved: originalSize - optimizedSize,
    settings: settings
  };
}

/**
 * Test optimization API payload
 */
export function testOptimizationAPI() {
  console.log('Testing optimization API payloads:');
  console.log('---');
  
  // Test upload payload
  const uploadPayload = {
    file: 'test-image.jpg',
    folder: 'posts',
    preset: 'featured-image',
    optimize: true,
    width: 1200,
    height: 630
  };
  
  console.log('Upload API Payload:');
  console.log(JSON.stringify(uploadPayload, null, 2));
  console.log('');
  
  // Test optimization payload
  const optimizePayload = {
    imageUrl: 'https://res.cloudinary.com/demo/image/upload/wikify-blog/user123/image.jpg',
    options: {
      width: 1200,
      height: 630,
      format: 'webp',
      quality: 'auto:best',
      crop: 'fill'
    }
  };
  
  console.log('Optimize API Payload:');
  console.log(JSON.stringify(optimizePayload, null, 2));
  console.log('');
  
  // Test expected response
  const expectedResponse = {
    success: true,
    data: {
      url: 'https://res.cloudinary.com/demo/image/upload/c_fill,f_webp,h_630,q_auto:best,w_1200/wikify-blog/user123/image.webp',
      public_id: 'wikify-blog/user123/image',
      width: 1200,
      height: 630,
      format: 'webp',
      size: 45678,
      compression_ratio: '32.5',
      optimized: true,
      preset: 'featured-image'
    }
  };
  
  console.log('Expected Response:');
  console.log(JSON.stringify(expectedResponse, null, 2));
}

/**
 * Run all optimization tests
 */
export function runAllOptimizationTests() {
  console.log('=== Image Optimization Tests ===\n');
  
  testPublicIdExtraction();
  console.log('\n');
  
  testOptimizationPresets();
  console.log('\n');
  
  testResponsiveImages();
  console.log('\n');
  
  // Simulate different file uploads
  const testFiles = [
    { name: 'large-photo.jpg', size: 5 * 1024 * 1024, preset: 'featured-image' as const },
    { name: 'profile-pic.png', size: 2 * 1024 * 1024, preset: 'avatar' as const },
    { name: 'thumbnail.gif', size: 500 * 1024, preset: 'thumbnail' as const },
  ];
  
  testFiles.forEach(file => {
    simulateUploadOptimization(file.name, file.size, file.preset);
    console.log('\n');
  });
  
  testOptimizationAPI();
  
  console.log('\n=== Tests Complete ===');
}
