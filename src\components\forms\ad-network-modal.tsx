'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface AdNetworkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (adCode: string, adType: string) => void;
}

const AD_NETWORKS = [
  { value: 'adsterra', label: 'Adsterra', description: 'Adsterra - High-performing ad network with global reach' },
  { value: 'facebook-audience', label: 'Facebook Audience Network', description: 'Facebook Audience Network' },
  { value: 'amazon-associates', label: 'Amazon Associates', description: 'Amazon Associates - Affiliate marketing' },
  { value: 'media-net', label: 'Media.net', description: 'Media.net - Yahoo and Bing ad network' },
  { value: 'propeller-ads', label: 'PropellerAds', description: 'PropellerAds - Popup and native ads' },
  { value: 'bidvertiser', label: 'BidVertiser', description: 'BidVertiser - PPC ad network' },
  { value: 'infolinks', label: 'InfoLinks', description: 'InfoLinks - In-text ads' },
  { value: 'chitika', label: 'Chitika', description: 'Chitika - Mobile ad network' },
  { value: 'custom', label: 'Custom Ad Code', description: 'Use your own custom ad code' },
];

const SAMPLE_CODES = {
  'adsterra': `<!-- Adsterra Banner Ad -->
<script type="text/javascript">
    atOptions = {
        'key' : 'XXXXXXXXXX',
        'format' : 'iframe',
        'height' : 250,
        'width' : 300,
        'params' : {}
    };
    document.write('<scr' + 'ipt type="text/javascript" src="http' + (location.protocol === 'https:' ? 's' : '') + '://www.profitabledisplaynetwork.com/XXXXXXXXXX/invoke.js"></scr' + 'ipt>');
</script>`,
  'facebook-audience': `<div id="fb-ad-container">
  <!-- Facebook Audience Network Ad Code -->
  <script>
    window.fbAsyncInit = function() {
      FB.init({
        appId: 'YOUR_APP_ID',
        xfbml: true,
        version: 'v18.0'
      });
    };
  </script>
  <div class="fb-ad" data-placementid="YOUR_PLACEMENT_ID"></div>
</div>`,
  'amazon-associates': `<iframe src="//rcm-na.amazon-adsystem.com/e/cm?o=1&p=8&l=as1&asins=PRODUCT_ID&ref=tf_til&fc1=000000&IS2=1&lt1=_blank&m=amazon&lc1=0000FF&bc1=000000&bg1=FFFFFF&f=ifr" 
        style="width:120px;height:240px;" 
        scrolling="no" 
        marginwidth="0" 
        marginheight="0" 
        frameborder="0">
</iframe>`,
  'media-net': `<script id="mNCC" language="javascript">
    medianet_width = "728";
    medianet_height = "90";
    medianet_crid = "XXXXXXXXXX";
    medianet_versionId = "XXXXXXXXXX";
</script>
<script src="//contextual.media.net/nmedianet.js?cid=XXXXXXXXXX"></script>`,
  'custom': `<!-- Paste your custom ad code here -->
<div class="ad-container">
  <!-- Place the code received from ad network here -->
</div>`
};

const AdNetworkModal: React.FC<AdNetworkModalProps> = ({
  isOpen,
  onClose,
  onInsert,
}) => {
  const [selectedNetwork, setSelectedNetwork] = useState('adsterra');
  const [adCode, setAdCode] = useState(SAMPLE_CODES['adsterra']);
  const [customCode, setCustomCode] = useState('');

  const handleNetworkChange = (network: string) => {
    setSelectedNetwork(network);
    if (network === 'custom') {
      setAdCode(customCode);
    } else {
      setAdCode(SAMPLE_CODES[network as keyof typeof SAMPLE_CODES] || '');
    }
  };

  const handleInsert = () => {
    const finalCode = selectedNetwork === 'custom' ? customCode : adCode;
    if (finalCode.trim()) {
      onInsert(finalCode, selectedNetwork);
      onClose();
      // Reset form
      setSelectedNetwork('adsterra');
      setAdCode(SAMPLE_CODES['adsterra']);
      setCustomCode('');
    }
  };

  const handleCancel = () => {
    onClose();
    // Reset form
    setSelectedNetwork('adsterra');
    setAdCode(SAMPLE_CODES['adsterra']);
    setCustomCode('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Add Ad Network Code
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Use ad network code to add advertisements to your post
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="mb-6">
            {/* Ad Network Selection */}
            <div>
              <label htmlFor="adNetwork" className="block text-sm font-medium text-gray-700 mb-3">
                Select Ad Network:
              </label>
              <select
                id="adNetwork"
                value={selectedNetwork}
                onChange={(e) => handleNetworkChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {AD_NETWORKS.map((network) => (
                  <option key={network.value} value={network.value}>
                    {network.label} - {network.description}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Code Input */}
          <div className="mt-6">
            <label htmlFor="adCode" className="block text-sm font-medium text-gray-700 mb-2">
              {selectedNetwork === 'custom' ? 'Your Custom Ad Code:' : 'Ad Code (edit if needed):'}
            </label>
            <textarea
              id="adCode"
              value={selectedNetwork === 'custom' ? customCode : adCode}
              onChange={(e) => {
                if (selectedNetwork === 'custom') {
                  setCustomCode(e.target.value);
                } else {
                  setAdCode(e.target.value);
                }
              }}
              placeholder={selectedNetwork === 'custom'
                ? "Paste the code received from your ad network here..."
                : "Ad code will be shown here..."
              }
              className="w-full h-64 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm resize-none"
              style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace' }}
            />
          </div>

          {/* Instructions */}
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Instructions:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Copy the correct code from your ad network account</li>
              <li>• Replace XXXXXXXXXX with your correct Publisher ID or Ad Unit ID</li>
              <li>• Preview before adding the code</li>
              <li>• Check if the ad code is working properly after publishing</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            type="button"
            variant="ghost"
            onClick={handleCancel}
            className="px-6 py-2"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleInsert}
            disabled={selectedNetwork === 'custom' ? !customCode.trim() : !adCode.trim()}
            className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add Ad Code
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdNetworkModal;
