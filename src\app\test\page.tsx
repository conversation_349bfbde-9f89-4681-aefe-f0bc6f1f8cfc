'use client';

import { useEffect, useState } from 'react';

interface DataVerification {
  success: boolean;
  data?: {
    counts: {
      users: number;
      posts: number;
      comments: number;
      categories: number;
      tags: number;
    };
    samples: {
      users: any[];
      posts: any[];
      categories: any[];
      tags: any[];
    };
  };
  error?: string;
}

export default function TestPage() {
  const [data, setData] = useState<DataVerification | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch('/api/verify-data');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching data:', error);
        setData({ success: false, error: 'Failed to fetch data' });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading data verification...</div>
      </div>
    );
  }

  if (!data || !data.success) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl text-red-600">
          Error: {data?.error || 'Unknown error'}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          WordPress Data Verification
        </h1>

        {/* Data Counts */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Users</h3>
            <p className="text-3xl font-bold text-blue-600">{data.data?.counts.users}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Posts</h3>
            <p className="text-3xl font-bold text-green-600">{data.data?.counts.posts}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Comments</h3>
            <p className="text-3xl font-bold text-yellow-600">{data.data?.counts.comments}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
            <p className="text-3xl font-bold text-purple-600">{data.data?.counts.categories}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Tags</h3>
            <p className="text-3xl font-bold text-red-600">{data.data?.counts.tags}</p>
          </div>
        </div>

        {/* Sample Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Users */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Users</h2>
            <div className="space-y-3">
              {data.data?.samples.users.map((user) => (
                <div key={user.id} className="border-b pb-2">
                  <p className="font-medium">{user.displayName || user.username}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                  <p className="text-xs text-gray-500">ID: {user.id} | Registered: {user.registered}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Posts */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Posts</h2>
            <div className="space-y-3">
              {data.data?.samples.posts.map((post) => (
                <div key={post.id} className="border-b pb-2">
                  <p className="font-medium">{post.title}</p>
                  <p className="text-sm text-gray-600">Slug: {post.slug}</p>
                  <p className="text-xs text-gray-500">
                    ID: {post.id} | Status: {post.status} | Author: {post.author} | Date: {post.date}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Categories */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Categories</h2>
            <div className="space-y-3">
              {data.data?.samples.categories.map((category) => (
                <div key={category.id} className="border-b pb-2">
                  <p className="font-medium">{category.name}</p>
                  <p className="text-sm text-gray-600">Slug: {category.slug}</p>
                  <p className="text-xs text-gray-500">ID: {category.id} | Count: {category.count}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Tags */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Tags</h2>
            <div className="space-y-3">
              {data.data?.samples.tags.map((tag) => (
                <div key={tag.id} className="border-b pb-2">
                  <p className="font-medium">{tag.name}</p>
                  <p className="text-sm text-gray-600">Slug: {tag.slug}</p>
                  <p className="text-xs text-gray-500">ID: {tag.id} | Count: {tag.count}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Test Links */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Links</h2>
          <div className="space-y-2">
            <a href="/auth/signin" className="block text-blue-600 hover:underline">
              → Test Login Page
            </a>
            <a href="/api/posts" className="block text-blue-600 hover:underline">
              → Test Posts API
            </a>
            <a href="/uploads/2024/11" className="block text-blue-600 hover:underline">
              → Test Uploads Directory (if exists)
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
