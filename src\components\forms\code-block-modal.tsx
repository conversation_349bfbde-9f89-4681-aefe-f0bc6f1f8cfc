'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface CodeBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (code: string, language: string) => void;
  initialCode?: string;
  initialLanguage?: string;
  isEditing?: boolean;
}

const LANGUAGES = [
  { value: 'html', label: 'HTML' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'css', label: 'CSS' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'php', label: 'PHP' },
  { value: 'sql', label: 'SQL' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'bash', label: 'Bash' },
];

const CodeBlockModal: React.FC<CodeBlockModalProps> = ({
  isOpen,
  onClose,
  onInsert,
  initialCode = '',
  initialLanguage = 'javascript',
  isEditing = false
}) => {
  const [code, setCode] = useState(initialCode);
  const [language, setLanguage] = useState(initialLanguage);

  const handleInsert = () => {
    onInsert(code, language);
    onClose();
    setCode('');
    setLanguage('javascript');
  };

  const handleCancel = () => {
    onClose();
    setCode(initialCode);
    setLanguage(initialLanguage);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {isEditing ? 'কোড ব্লক এডিট করুন' : 'কোড ব্লক যুক্ত করুন'}
          </h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-hidden flex flex-col">
          {/* Language Selection */}
          <div className="mb-4">
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
              Select Programming Language:
            </label>
            <select
              id="language"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {LANGUAGES.map((lang) => (
                <option key={lang.value} value={lang.value}>
                  {lang.label}
                </option>
              ))}
            </select>
          </div>

          {/* Code Input */}
          <div className="flex-1 flex flex-col">
            <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
              Write your code:
            </label>
            <textarea
              id="code"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="Write your code here..."
              className="flex-1 min-h-[300px] px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm resize-none"
              style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace' }}
            />
          </div>

          {/* Preview */}
          {code && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                প্রিভিউ:
              </label>
              <div className="bg-gray-900 text-gray-100 rounded-md p-4 overflow-auto max-h-32">
                <pre className="text-sm">
                  <code className={`language-${language}`}>{code}</code>
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            type="button"
            variant="ghost"
            onClick={handleCancel}
            className="px-6 py-2"
          >
            বাতিল
          </Button>
          <Button
            type="button"
            onClick={handleInsert}
            disabled={!code.trim()}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isEditing ? 'আপডেট করুন' : 'কোড যুক্ত করুন'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CodeBlockModal;
