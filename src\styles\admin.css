/* Admin Layout Enhancements */

/* Fix for sidebar and main content layout */
.admin-layout {
  min-height: 100vh;
  position: relative;
}

/* Mobile layout */
@media (max-width: 1023px) {
  .admin-main-content {
    width: 100% !important;
    margin-left: 0 !important;
  }

  .admin-sidebar {
    width: 288px !important; /* w-72 */
  }
}

/* Desktop layout */
@media (min-width: 1024px) {
  .admin-layout {
    position: relative;
  }

  .admin-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    z-index: 50 !important;
    transform: translateX(0) !important;
  }

  .admin-main-content {
    margin-left: 288px !important; /* Default sidebar width (w-72) */
    transition: margin-left 0.3s ease-in-out !important;
    min-height: 100vh;
    width: auto !important;
    flex: none !important;
  }

  /* When sidebar is collapsed */
  .admin-layout.sidebar-collapsed .admin-main-content {
    margin-left: 64px !important; /* Collapsed sidebar width (w-16) */
  }

  /* Ensure sidebar width changes are applied */
  .admin-layout.sidebar-collapsed .admin-sidebar {
    width: 64px !important; /* w-16 */
  }

  .admin-layout:not(.sidebar-collapsed) .admin-sidebar {
    width: 288px !important; /* w-72 */
  }
}

/* Smooth transitions for all admin elements */
.admin-layout * {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for sidebar */
.admin-sidebar::-webkit-scrollbar {
  width: 4px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Dark mode scrollbar */
.dark .admin-sidebar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* Enhanced focus states */
.admin-nav-item:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(8px)) {
  .admin-header {
    backdrop-filter: blur(8px);
  }
}

/* Animation for sidebar collapse */
.sidebar-collapsed .sidebar-text {
  opacity: 0;
  transform: translateX(-10px);
}

.sidebar-expanded .sidebar-text {
  opacity: 1;
  transform: translateX(0);
}

/* Card hover effects */
.admin-card {
  transform: translateY(0);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.admin-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading states */
.admin-loading {
  position: relative;
  overflow: hidden;
}

.admin-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Dark mode loading shimmer */
.dark .admin-loading::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

/* Status indicators */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

.status-indicator.status-success::before {
  background: #10b981;
}

.status-indicator.status-warning::before {
  background: #f59e0b;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive grid improvements */
@media (max-width: 640px) {
  .admin-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .admin-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .admin-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
}

/* Print styles for admin pages */
@media print {
  .admin-sidebar,
  .admin-header,
  .admin-actions {
    display: none !important;
  }
  
  .admin-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .admin-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-nav-item {
    border: 1px solid transparent;
  }
  
  .admin-nav-item:hover,
  .admin-nav-item.active {
    border-color: currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .admin-layout *,
  .admin-card,
  .sidebar-text {
    transition: none !important;
    animation: none !important;
  }
}

/* Focus management for accessibility */
.admin-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.admin-skip-link:focus {
  top: 6px;
}

/* Admin-specific styles using global theme variables */
.admin-layout {
  background-color: var(--content-bg);
  color: var(--foreground);
}

.admin-sidebar {
  background-color: var(--sidebar-bg);
  border-color: var(--sidebar-border);
  color: var(--sidebar-text);
}

.admin-card {
  background-color: var(--content-card);
  border-color: var(--content-border);
  color: var(--card-foreground);
}

.admin-header {
  background-color: var(--header-bg);
  border-color: var(--header-border);
  color: var(--header-text);
}
