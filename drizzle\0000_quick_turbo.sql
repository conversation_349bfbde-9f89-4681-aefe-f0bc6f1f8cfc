CREATE TABLE `accounts` (
	`user_id` varchar(255) NOT NULL,
	`type` varchar(255) NOT NULL,
	`provider` varchar(255) NOT NULL,
	`provider_account_id` varchar(255) NOT NULL,
	`refresh_token` text,
	`access_token` text,
	`expires_at` int,
	`token_type` varchar(255),
	`scope` varchar(255),
	`id_token` text,
	`session_state` varchar(255),
	CONSTRAINT `accounts_provider_provider_account_id_pk` PRIMARY KEY(`provider`,`provider_account_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_comments` (
	`comment_ID` bigint unsigned AUTO_INCREMENT NOT NULL,
	`comment_post_ID` bigint unsigned NOT NULL DEFAULT 0,
	`comment_author` text NOT NULL,
	`comment_author_email` varchar(100) NOT NULL DEFAULT '',
	`comment_author_url` varchar(200) NOT NULL DEFAULT '',
	`comment_author_IP` varchar(100) NOT NULL DEFAULT '',
	`comment_date` datetime NOT NULL,
	`comment_date_gmt` datetime NOT NULL,
	`comment_content` text NOT NULL,
	`comment_karma` int NOT NULL DEFAULT 0,
	`comment_approved` varchar(20) NOT NULL DEFAULT '1',
	`comment_agent` varchar(255) NOT NULL DEFAULT '',
	`comment_type` varchar(20) NOT NULL DEFAULT 'comment',
	`comment_parent` bigint unsigned NOT NULL DEFAULT 0,
	`user_id` bigint unsigned NOT NULL DEFAULT 0,
	CONSTRAINT `wikify1h_comments_comment_ID` PRIMARY KEY(`comment_ID`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_options` (
	`option_id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`option_name` varchar(191) NOT NULL DEFAULT '',
	`option_value` longtext NOT NULL,
	`autoload` varchar(20) NOT NULL DEFAULT 'yes',
	CONSTRAINT `wikify1h_options_option_id` PRIMARY KEY(`option_id`)
);
--> statement-breakpoint
CREATE TABLE `password_reset_tokens` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`email` varchar(100) NOT NULL,
	`token` varchar(255) NOT NULL,
	`expires` datetime NOT NULL,
	`used` boolean NOT NULL DEFAULT false,
	`created_at` datetime NOT NULL,
	CONSTRAINT `password_reset_tokens_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_postmeta` (
	`meta_id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`post_id` bigint unsigned NOT NULL DEFAULT 0,
	`meta_key` varchar(255),
	`meta_value` longtext,
	CONSTRAINT `wikify1h_postmeta_meta_id` PRIMARY KEY(`meta_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_posts` (
	`ID` bigint unsigned AUTO_INCREMENT NOT NULL,
	`post_author` bigint unsigned NOT NULL DEFAULT 0,
	`post_date` datetime NOT NULL,
	`post_date_gmt` datetime NOT NULL,
	`post_content` longtext NOT NULL,
	`post_title` text NOT NULL,
	`post_excerpt` text NOT NULL,
	`post_status` varchar(20) NOT NULL DEFAULT 'publish',
	`comment_status` varchar(20) NOT NULL DEFAULT 'open',
	`ping_status` varchar(20) NOT NULL DEFAULT 'open',
	`post_password` varchar(255) NOT NULL DEFAULT '',
	`post_name` varchar(200) NOT NULL DEFAULT '',
	`to_ping` text NOT NULL,
	`pinged` text NOT NULL,
	`post_modified` datetime NOT NULL,
	`post_modified_gmt` datetime NOT NULL,
	`post_content_filtered` longtext NOT NULL,
	`post_parent` bigint unsigned NOT NULL DEFAULT 0,
	`guid` varchar(255) NOT NULL DEFAULT '',
	`menu_order` int NOT NULL DEFAULT 0,
	`post_type` varchar(20) NOT NULL DEFAULT 'post',
	`post_mime_type` varchar(100) NOT NULL DEFAULT '',
	`comment_count` bigint NOT NULL DEFAULT 0,
	CONSTRAINT `wikify1h_posts_ID` PRIMARY KEY(`ID`)
);
--> statement-breakpoint
CREATE TABLE `sessions` (
	`session_token` varchar(255) NOT NULL,
	`user_id` varchar(255) NOT NULL,
	`expires` timestamp NOT NULL,
	CONSTRAINT `sessions_session_token` PRIMARY KEY(`session_token`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_term_relationships` (
	`object_id` bigint unsigned NOT NULL DEFAULT 0,
	`term_taxonomy_id` bigint unsigned NOT NULL DEFAULT 0,
	`term_order` int NOT NULL DEFAULT 0,
	CONSTRAINT `wikify1h_term_relationships_object_id_term_taxonomy_id_pk` PRIMARY KEY(`object_id`,`term_taxonomy_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_term_taxonomy` (
	`term_taxonomy_id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`term_id` bigint unsigned NOT NULL DEFAULT 0,
	`taxonomy` varchar(32) NOT NULL DEFAULT '',
	`description` longtext NOT NULL,
	`parent` bigint unsigned NOT NULL DEFAULT 0,
	`count` bigint NOT NULL DEFAULT 0,
	CONSTRAINT `wikify1h_term_taxonomy_term_taxonomy_id` PRIMARY KEY(`term_taxonomy_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_terms` (
	`term_id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(200) NOT NULL DEFAULT '',
	`slug` varchar(200) NOT NULL DEFAULT '',
	`term_group` bigint NOT NULL DEFAULT 0,
	CONSTRAINT `wikify1h_terms_term_id` PRIMARY KEY(`term_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_usermeta` (
	`umeta_id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`user_id` bigint unsigned NOT NULL DEFAULT 0,
	`meta_key` varchar(255),
	`meta_value` longtext,
	CONSTRAINT `wikify1h_usermeta_umeta_id` PRIMARY KEY(`umeta_id`)
);
--> statement-breakpoint
CREATE TABLE `wikify1h_users` (
	`ID` bigint unsigned AUTO_INCREMENT NOT NULL,
	`user_login` varchar(60) NOT NULL DEFAULT '',
	`user_pass` varchar(255) NOT NULL DEFAULT '',
	`user_nicename` varchar(50) NOT NULL DEFAULT '',
	`user_email` varchar(100) NOT NULL DEFAULT '',
	`user_url` varchar(100) NOT NULL DEFAULT '',
	`user_registered` datetime NOT NULL,
	`user_activation_key` varchar(255) NOT NULL DEFAULT '',
	`user_status` int NOT NULL DEFAULT 0,
	`display_name` varchar(250) NOT NULL DEFAULT '',
	CONSTRAINT `wikify1h_users_ID` PRIMARY KEY(`ID`)
);
--> statement-breakpoint
CREATE TABLE `verification_tokens` (
	`identifier` varchar(255) NOT NULL,
	`token` varchar(255) NOT NULL,
	`expires` timestamp NOT NULL,
	CONSTRAINT `verification_tokens_identifier_token_pk` PRIMARY KEY(`identifier`,`token`)
);
--> statement-breakpoint
CREATE INDEX `user_idx` ON `accounts` (`user_id`);--> statement-breakpoint
CREATE INDEX `comment_post_ID` ON `wikify1h_comments` (`comment_post_ID`);--> statement-breakpoint
CREATE INDEX `comment_approved_date_gmt` ON `wikify1h_comments` (`comment_approved`,`comment_date_gmt`);--> statement-breakpoint
CREATE INDEX `comment_date_gmt` ON `wikify1h_comments` (`comment_date_gmt`);--> statement-breakpoint
CREATE INDEX `comment_parent` ON `wikify1h_comments` (`comment_parent`);--> statement-breakpoint
CREATE INDEX `comment_author_email` ON `wikify1h_comments` (`comment_author_email`);--> statement-breakpoint
CREATE INDEX `option_name` ON `wikify1h_options` (`option_name`);--> statement-breakpoint
CREATE INDEX `email_idx` ON `password_reset_tokens` (`email`);--> statement-breakpoint
CREATE INDEX `token_idx` ON `password_reset_tokens` (`token`);--> statement-breakpoint
CREATE INDEX `expires_idx` ON `password_reset_tokens` (`expires`);--> statement-breakpoint
CREATE INDEX `post_id` ON `wikify1h_postmeta` (`post_id`);--> statement-breakpoint
CREATE INDEX `meta_key` ON `wikify1h_postmeta` (`meta_key`);--> statement-breakpoint
CREATE INDEX `post_name` ON `wikify1h_posts` (`post_name`);--> statement-breakpoint
CREATE INDEX `type_status_date` ON `wikify1h_posts` (`post_type`,`post_status`,`post_date`,`ID`);--> statement-breakpoint
CREATE INDEX `post_parent` ON `wikify1h_posts` (`post_parent`);--> statement-breakpoint
CREATE INDEX `post_author` ON `wikify1h_posts` (`post_author`);--> statement-breakpoint
CREATE INDEX `user_idx` ON `sessions` (`user_id`);--> statement-breakpoint
CREATE INDEX `term_taxonomy_id` ON `wikify1h_term_relationships` (`term_taxonomy_id`);--> statement-breakpoint
CREATE INDEX `term_id_taxonomy` ON `wikify1h_term_taxonomy` (`term_id`,`taxonomy`);--> statement-breakpoint
CREATE INDEX `taxonomy` ON `wikify1h_term_taxonomy` (`taxonomy`);--> statement-breakpoint
CREATE INDEX `slug` ON `wikify1h_terms` (`slug`);--> statement-breakpoint
CREATE INDEX `name` ON `wikify1h_terms` (`name`);--> statement-breakpoint
CREATE INDEX `user_id` ON `wikify1h_usermeta` (`user_id`);--> statement-breakpoint
CREATE INDEX `meta_key` ON `wikify1h_usermeta` (`meta_key`);--> statement-breakpoint
CREATE INDEX `user_login_key` ON `wikify1h_users` (`user_login`);--> statement-breakpoint
CREATE INDEX `user_nicename` ON `wikify1h_users` (`user_nicename`);--> statement-breakpoint
CREATE INDEX `user_email` ON `wikify1h_users` (`user_email`);