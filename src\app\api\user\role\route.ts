import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserRole } from '@/lib/utils/database';

// GET /api/user/role - Get current user's role
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user role from database
    const userRole = await getUserRole(parseInt(session.user.id));

    return NextResponse.json({
      success: true,
      role: userRole,
      user: {
        id: session.user.id,
        username: session.user.username,
        email: session.user.email
      }
    });

  } catch (error) {
    console.error('Error getting user role:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get user role',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
