import { db } from '@/lib/db';
import { notifications } from '@/lib/db/schema';
import { eq, and, count } from 'drizzle-orm';

export interface CreateNotificationParams {
  userId: number;
  type: 'post_edited' | 'post_deleted';
  title: string;
  message: string;
  postId?: number;
  postTitle?: string;
  adminId: number;
  adminName: string;
}

/**
 * Create a notification for a user
 */
export async function createNotification(params: CreateNotificationParams): Promise<boolean> {
  try {
    await db
      .insert(notifications)
      .values({
        user_id: params.userId,
        type: params.type,
        title: params.title,
        message: params.message,
        post_id: params.postId || null,
        post_title: params.postTitle || null,
        admin_id: params.adminId,
        admin_name: params.adminName,
        is_read: false,
        created_at: new Date(),
      });

    return true;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
}

/**
 * Create notification when admin edits a user's post
 */
export async function createPostEditedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string
): Promise<boolean> {
  return createNotification({
    userId,
    type: 'post_edited',
    title: 'Your post has been edited',
    message: `An admin (${adminName}) has edited your post "${postTitle}". You can review the changes in your dashboard.`,
    postId,
    postTitle,
    adminId,
    adminName,
  });
}

/**
 * Create notification when admin deletes a user's post
 */
export async function createPostDeletedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string
): Promise<boolean> {
  return createNotification({
    userId,
    type: 'post_deleted',
    title: 'Your post has been deleted',
    message: `An admin (${adminName}) has deleted your post "${postTitle}". If you have questions about this action, please contact support.`,
    postId,
    postTitle,
    adminId,
    adminName,
  });
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: number): Promise<number> {
  try {
    const result = await db
      .select({ count: count() })
      .from(notifications)
      .where(
        and(
          eq(notifications.user_id, userId),
          eq(notifications.is_read, false)
        )
      );

    return result[0]?.count || 0;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return 0;
  }
}
