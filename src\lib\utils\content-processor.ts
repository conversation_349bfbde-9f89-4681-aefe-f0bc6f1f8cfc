/**
 * Process WordPress content to handle images and other media
 */

export function processWordPressContent(content: string, baseUrl: string = ''): string {
  if (!content) return '';

  let processedContent = content;

  // Replace WordPress upload URLs with our API route
  // Pattern: wp-content/uploads/YYYY/MM/filename.ext
  processedContent = processedContent.replace(
    /wp-content\/uploads\//g,
    '/uploads/'
  );

  // Handle relative upload paths
  processedContent = processedContent.replace(
    /src="uploads\//g,
    'src="/uploads/'
  );

  // Handle absolute URLs that might point to old domain
  if (baseUrl) {
    // Replace old domain URLs with current domain
    const urlPattern = /https?:\/\/[^\/]+\/wp-content\/uploads\//g;
    processedContent = processedContent.replace(urlPattern, '/uploads/');
  }

  // Fix any double slashes
  processedContent = processedContent.replace(/\/\/uploads\//g, '/uploads/');

  return processedContent;
}

/**
 * Extract featured image from post content or meta
 */
export function extractFeaturedImage(content: string): string | null {
  // Look for first image in content
  const imgMatch = content.match(/<img[^>]+src="([^"]+)"[^>]*>/i);
  if (imgMatch && imgMatch[1]) {
    return processWordPressContent(imgMatch[1]);
  }
  return null;
}

/**
 * Get featured image from postmeta or content
 */
export async function getFeaturedImage(postId: number, content: string): Promise<string | null> {
  try {
    const { db } = await import('@/lib/db');
    const { postmeta } = await import('@/lib/db/schema');
    const { eq, and, or } = await import('drizzle-orm');

    // First check for WordPress-style _thumbnail_id
    const thumbnailIdResult = await db
      .select()
      .from(postmeta)
      .where(
        and(
          eq(postmeta.post_id, postId),
          eq(postmeta.meta_key, '_thumbnail_id')
        )
      )
      .limit(1);

    if (thumbnailIdResult.length > 0 && thumbnailIdResult[0].meta_value) {
      const attachmentId = parseInt(thumbnailIdResult[0].meta_value);

      // Get the attachment file path
      const attachmentResult = await db
        .select()
        .from(postmeta)
        .where(
          and(
            eq(postmeta.post_id, attachmentId),
            eq(postmeta.meta_key, '_wp_attached_file')
          )
        )
        .limit(1);

      if (attachmentResult.length > 0 && attachmentResult[0].meta_value) {
        let imagePath = attachmentResult[0].meta_value;

        // Ensure the path starts with /uploads/
        if (!imagePath.startsWith('/uploads/') && !imagePath.startsWith('http')) {
          imagePath = `/uploads/${imagePath}`;
        }

        return processWordPressContent(imagePath);
      }
    }

    // Fallback: check postmeta for direct featured image references
    const metaResult = await db
      .select()
      .from(postmeta)
      .where(
        and(
          eq(postmeta.post_id, postId),
          or(
            eq(postmeta.meta_key, '_thumbnail_url'),
            eq(postmeta.meta_key, '_wp_attached_file')
          )
        )
      )
      .limit(1);

    if (metaResult.length > 0 && metaResult[0].meta_value) {
      let imagePath = metaResult[0].meta_value;

      // Ensure the path starts with /uploads/
      if (!imagePath.startsWith('/uploads/') && !imagePath.startsWith('http')) {
        imagePath = `/uploads/${imagePath}`;
      }

      return processWordPressContent(imagePath);
    }

    // Fallback to extracting from content
    return extractFeaturedImage(content);
  } catch (error) {
    console.error('Error getting featured image:', error);
    // Fallback to extracting from content
    return extractFeaturedImage(content);
  }
}

/**
 * Generate excerpt from content
 */
export function generateExcerpt(content: string, length: number = 150): string {
  if (!content) return '';

  // Remove HTML tags
  const textContent = content.replace(/<[^>]*>/g, '');
  
  // Remove extra whitespace
  const cleanText = textContent.replace(/\s+/g, ' ').trim();
  
  if (cleanText.length <= length) {
    return cleanText;
  }
  
  // Cut at word boundary
  const truncated = cleanText.substring(0, length);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
}

/**
 * Clean and format WordPress content for display
 */
export function formatPostContent(content: string): string {
  if (!content) return '';

  let formatted = processWordPressContent(content);

  // Add responsive classes to images
  formatted = formatted.replace(
    /<img([^>]*)>/g,
    '<img$1 class="max-w-full h-auto rounded-lg shadow-sm">'
  );

  // Add styling to blockquotes
  formatted = formatted.replace(
    /<blockquote([^>]*)>/g,
    '<blockquote$1 class="border-l-4 border-blue-500 pl-4 italic text-gray-700 my-4">'
  );

  // Add styling to code blocks
  formatted = formatted.replace(
    /<pre([^>]*)>/g,
    '<pre$1 class="bg-gray-100 p-4 rounded-lg overflow-x-auto">'
  );

  formatted = formatted.replace(
    /<code([^>]*)>/g,
    '<code$1 class="bg-gray-100 px-2 py-1 rounded text-sm">'
  );

  return formatted;
}

/**
 * Get all images from content
 */
export function extractImagesFromContent(content: string): string[] {
  if (!content) return [];

  const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/gi;
  const images: string[] = [];
  let match;

  while ((match = imgRegex.exec(content)) !== null) {
    const processedSrc = processWordPressContent(match[1]);
    images.push(processedSrc);
  }

  return images;
}

/**
 * Check if an upload file exists
 */
export async function checkUploadExists(filePath: string): Promise<boolean> {
  try {
    const response = await fetch(`/uploads/${filePath}`, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}
