'use client';

import { useState, useEffect } from 'react';
import Layout from "@/components/layout/layout";
import HomePostCard from '@/components/blog/home-post-card';
import CategoryCard from '@/components/categories/category-card';

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  date: string;
  featured_image?: string;
  author?: {
    id: number;
    username: string;
    displayName: string;
  };
  commentCount: number;
}

interface BlogResponse {
  success: boolean;
  data: BlogPost[];
  pagination: {
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

interface Category {
  id: number;
  term_id: number;
  name: string;
  slug: string;
  description: string;
  count: number;
  parent: number;
}

export default function Home() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  const handlePostDeleted = () => {
    // Refresh the posts list
    fetchPosts(1, true);
  };

  useEffect(() => {
    fetchPosts(1, true);
    fetchCategories();
  }, []);

  const fetchPosts = async (page: number = 1, reset: boolean = false) => {
    if (reset) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const params = new URLSearchParams();
      params.set('page', page.toString());
      params.set('limit', '8');
      params.set('status', 'publish');

      const response = await fetch(`/api/posts?${params.toString()}`);
      const result: BlogResponse = await response.json();

      if (result.success) {
        if (reset) {
          setPosts(result.data);
        } else {
          // Filter out duplicates by checking if post ID already exists
          setPosts(prevPosts => {
            const existingIds = new Set(prevPosts.map(post => post.id));
            const newPosts = result.data.filter(post => !existingIds.has(post.id));
            return [...prevPosts, ...newPosts];
          });
        }
        setHasMore(result.pagination.hasMore);
        setCurrentPage(page);
      } else {
        console.error('Failed to fetch posts:', result);
        if (reset) {
          setPosts([]);
        }
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      if (reset) {
        setPosts([]);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchPosts(currentPage + 1, false);
    }
  };

  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await fetch('/api/categories');
      const result = await response.json();

      if (result.success) {
        setCategories(result.data);
      } else {
        console.error('Failed to fetch categories:', result.error);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setCategoriesLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen" style={{ backgroundColor: '#F0F2F6' }}>
        {/* Categories Section */}
        <div style={{ backgroundColor: '#F0F2F6' }} className="border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {categoriesLoading ? (
              <div className="flex overflow-x-auto gap-3 pb-2 scrollbar-hide">
                {[...Array(15)].map((_, i) => (
                  <div key={i} className="animate-pulse flex-shrink-0">
                    <div className="bg-gray-200 rounded-full h-8 w-24"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex overflow-x-auto gap-4 pb-4 scrollbar-hide">
                {categories.map((category) => (
                  <div key={category.id} className="flex-shrink-0">
                    <CategoryCard
                      category={category}
                      size="small"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Posts Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">


          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl overflow-hidden shadow-lg animate-pulse">
                  <div className="bg-gray-200 h-48"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-3"></div>
                    <div className="h-6 bg-gray-200 rounded mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="flex justify-between">
                      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : posts.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {posts.map((post, index) => (
                  <HomePostCard
                    key={post.id}
                    post={post}
                    author={post.author}
                    onPostDeleted={handlePostDeleted}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center mt-12">
                  <button
                    onClick={handleLoadMore}
                    disabled={loadingMore}
                    className="bg-white hover:bg-gray-50 text-gray-700 font-semibold py-3 px-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200"
                  >
                    {loadingMore ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <span>Load More</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    )}
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No posts found.</p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
