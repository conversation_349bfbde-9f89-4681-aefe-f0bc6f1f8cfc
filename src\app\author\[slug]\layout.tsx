import { Metadata } from 'next';
import { getAuthorBySlug } from '@/lib/utils/database';
import { getAbsoluteImageUrl, getDefaultAvatarUrl } from '@/lib/utils/image-utils';

interface Props {
  params: Promise<{ slug: string }>;
  children: React.ReactNode;
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  try {
    const { slug } = await params;
    const authorData = await getAuthorBySlug(slug);

    if (!authorData) {
      return {
        title: 'Author Not Found - Wikify',
        description: 'The requested author profile could not be found.',
      };
    }

    const authorName = authorData.displayName;
    const bio = authorData.bio || `${authorName} is a contributor on Wikify, sharing comprehensive guides and tutorials.`;
    const profileImage = getAbsoluteImageUrl(authorData.profileImage) || getDefaultAvatarUrl(authorName);
    
    // Get base URL for canonical URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const canonicalUrl = `${baseUrl}/author/${slug}`;

    const title = `${authorName} - Author Profile`;
    const description = bio.length > 160 ? `${bio.substring(0, 157)}...` : bio;

    return {
      title: `${title} - Wikify`,
      description: description,
      keywords: `${authorName}, wikify, author, guides, tutorials, profile`,
      authors: [{ name: authorName }],
      creator: authorName,
      publisher: 'Wikify',
      applicationName: 'Wikify',
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: title,
        description: description,
        siteName: 'Wikify',
        type: 'profile',
        url: canonicalUrl,
        images: [
          {
            url: profileImage,
            width: 400,
            height: 400,
            alt: `${authorName} - Profile Picture`,
          }
        ],
        profile: {
          firstName: authorName.split(' ')[0] || authorName,
          lastName: authorName.split(' ').slice(1).join(' ') || '',
          username: authorData.username,
        },
      },
      twitter: {
        card: 'summary',
        title: title,
        description: description,
        images: [profileImage],
        creator: `@${authorData.username}`,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata for author:', error);
    return {
      title: 'Wikify - How to Guide you can Trust',
      description: 'Your trusted source for comprehensive guides and tutorials.',
    };
  }
}

export default function AuthorLayout({ children }: { children: React.ReactNode }) {
  return children;
}
