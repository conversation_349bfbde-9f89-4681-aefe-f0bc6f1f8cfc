import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships } from '@/lib/db/schema';
import { eq, desc, like, count, sql, and } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils/slug';

// GET /api/categories - List categories with pagination and search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const search = searchParams.get('search');
    const includeEmpty = searchParams.get('include_empty') === 'true';
    
    const offset = (page - 1) * limit;

    // Build query for categories
    let query = db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .innerJoin(term_taxonomy, and(
        eq(terms.term_id, term_taxonomy.term_id),
        eq(term_taxonomy.taxonomy, 'category')
      ))
      .orderBy(desc(term_taxonomy.count), terms.name)
      .limit(limit)
      .offset(offset);

    if (search) {
      query = query.where(like(terms.name, `%${search}%`));
    }

    // For post forms, we want to include all categories, even empty ones
    // Only filter empty categories if explicitly requested
    if (!includeEmpty && searchParams.get('for_posts') !== 'true') {
      query = query.where(sql`${term_taxonomy.count} > 0`);
    }

    const result = await query;

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(terms)
      .innerJoin(term_taxonomy, and(
        eq(terms.term_id, term_taxonomy.term_id),
        eq(term_taxonomy.taxonomy, 'category')
      ));

    // Process categories
    const categories = result.map(({ term, taxonomy }) => ({
      id: term?.term_id,
      name: term?.name,
      slug: term?.slug,
      description: taxonomy?.description || '',
      count: taxonomy?.count || 0,
      parent: taxonomy?.parent || 0
    }));

    return NextResponse.json({
      success: true,
      data: categories,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch categories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create new category (Admin/Editor only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, slug, description = '', parent = 0 } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Category name is required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    const categorySlug = slug || generateSlug(name);

    // Check if category with same name or slug exists
    const existingCategory = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .where(eq(terms.name, name))
      .limit(1);

    if (existingCategory.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Category with this name already exists' },
        { status: 400 }
      );
    }

    const existingSlug = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .where(eq(terms.slug, categorySlug))
      .limit(1);

    if (existingSlug.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Category with this slug already exists' },
        { status: 400 }
      );
    }

    // Create term
    const termResult = await db.insert(terms).values({
      name,
      slug: categorySlug,
      term_group: 0
    });

    const termId = termResult.insertId;

    // Create taxonomy entry
    await db.insert(term_taxonomy).values({
      term_id: termId,
      taxonomy: 'category',
      description,
      parent,
      count: 0
    });

    return NextResponse.json({
      success: true,
      data: {
        id: termId,
        name,
        slug: categorySlug,
        description,
        parent,
        count: 0
      }
    });

  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create category',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/categories - Bulk update categories (Admin/Editor only)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, categoryIds } = body;

    if (!action || !categoryIds || !Array.isArray(categoryIds)) {
      return NextResponse.json(
        { success: false, error: 'Action and category IDs are required' },
        { status: 400 }
      );
    }

    if (action === 'delete') {
      // Delete categories and their taxonomy entries
      for (const categoryId of categoryIds) {
        // Delete term relationships first
        await db
          .delete(term_relationships)
          .where(eq(term_relationships.term_taxonomy_id, categoryId));

        // Delete taxonomy entry
        await db
          .delete(term_taxonomy)
          .where(eq(term_taxonomy.term_id, categoryId));

        // Delete term
        await db
          .delete(terms)
          .where(eq(terms.term_id, categoryId));
      }

      return NextResponse.json({
        success: true,
        message: `${categoryIds.length} categories deleted successfully`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error bulk updating categories:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update categories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
