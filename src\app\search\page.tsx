'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Layout from '@/components/layout/layout';
import SearchBar from '@/components/blog/search-bar';
import PostFilters from '@/components/blog/post-filters';
import PostCard from '@/components/blog/post-card';
import Pagination from '@/components/ui/pagination';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SearchResult {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  date: string;
  featured_image?: string;
  author?: {
    id: number;
    username: string;
    displayName: string;
  };
  commentCount: number;
}

interface SearchResponse {
  success: boolean;
  data: SearchResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  query: {
    search: string;
    category?: string;
    tag?: string;
    author?: string;
  };
}

interface FilterOption {
  id: string;
  name: string;
  slug: string;
  count?: number;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [filtersLoading, setFiltersLoading] = useState(true);
  const [categories, setCategories] = useState<FilterOption[]>([]);
  const [tags, setTags] = useState<FilterOption[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    hasMore: false,
  });
  const [query, setQuery] = useState({
    search: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    tag: searchParams.get('tag') || '',
    author: searchParams.get('author') || '',
  });
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'date' | 'relevance'>('relevance');

  const handlePostDeleted = () => {
    // Refresh the search results
    performSearch(pagination.page);
  };

  useEffect(() => {
    const newQuery = {
      search: searchParams.get('q') || '',
      category: searchParams.get('category') || '',
      tag: searchParams.get('tag') || '',
      author: searchParams.get('author') || '',
    };
    setQuery(newQuery);
    performSearch(newQuery, 1);
    fetchFilters();
  }, [searchParams]);

  const fetchFilters = async () => {
    try {
      setFiltersLoading(true);

      // Fetch categories
      const categoriesResponse = await fetch('/api/categories?include_empty=false');
      const categoriesResult = await categoriesResponse.json();

      if (categoriesResult.success) {
        const formattedCategories = categoriesResult.data.map((cat: any) => ({
          id: cat.id.toString(),
          name: cat.name,
          slug: cat.slug,
          count: cat.count
        }));
        setCategories(formattedCategories);
      }

      // Fetch tags
      const tagsResponse = await fetch('/api/tags?include_empty=false');
      const tagsResult = await tagsResponse.json();

      if (tagsResult.success) {
        const formattedTags = tagsResult.data.map((tag: any) => ({
          id: tag.id.toString(),
          name: tag.name,
          slug: tag.slug,
          count: tag.count
        }));
        setTags(formattedTags);
      }

    } catch (error) {
      console.error('Error fetching filters:', error);
    } finally {
      setFiltersLoading(false);
    }
  };

  // Generate suggestions when categories and tags are loaded
  useEffect(() => {
    if (categories.length > 0 || tags.length > 0) {
      generateSearchSuggestions();
    }
  }, [categories, tags]);

  // Generate search suggestions based on categories and tags
  const generateSearchSuggestions = () => {
    const suggestions = [];

    // Add popular categories
    const popularCategories = categories.slice(0, 5);
    popularCategories.forEach(cat => {
      suggestions.push(cat.name);
    });

    // Add popular tags
    const popularTags = tags.slice(0, 5);
    popularTags.forEach(tag => {
      suggestions.push(tag.name);
    });

    // Add some common search terms
    const commonTerms = [
      'tutorial', 'guide', 'tips', 'how to', 'best practices',
      'beginner', 'advanced', 'review', 'comparison', 'latest'
    ];
    suggestions.push(...commonTerms);

    setSearchSuggestions(suggestions.slice(0, 10));
  };

  const performSearch = async (searchQuery: any, page: number = 1) => {
    setLoading(true);
    
    try {
      const params = new URLSearchParams();
      if (searchQuery.search) params.set('q', searchQuery.search);
      if (searchQuery.category) params.set('category', searchQuery.category);
      if (searchQuery.tag) params.set('tag', searchQuery.tag);
      if (searchQuery.author) params.set('author', searchQuery.author);
      params.set('page', page.toString());
      params.set('limit', '12');

      const response = await fetch(`/api/search?${params.toString()}`);
      const result: SearchResponse = await response.json();

      if (result.success) {
        setResults(result.data);
        setPagination(result.pagination);
      } else {
        console.error('Search failed:', result);
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    performSearch(query, page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getSearchTitle = () => {
    const parts = [];
    if (query.search) parts.push(`"${query.search}"`);
    if (query.category) parts.push(`in ${query.category}`);
    if (query.tag) parts.push(`tagged ${query.tag}`);
    
    if (parts.length === 0) {
      return 'All Posts';
    }
    
    return `Search results for ${parts.join(' ')}`;
  };

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="mb-6">
              <span className="text-6xl">🔍</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Search & Discover
            </h1>
            <div className="max-w-3xl mx-auto">
              <SearchBar placeholder="Search articles, authors, topics..." />
            </div>
          </div>
        </div>
      </section>

      <div className="min-h-screen" style={{ backgroundColor: '#F0F2F6' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search Results Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {getSearchTitle()}
              </h2>
              {!loading && (
                <div className="flex items-center gap-4 text-gray-600">
                  <span>{pagination.total} results found</span>
                  {(query.search || query.category || query.tag) && (
                    <span className="text-sm">
                      • Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Sort Options */}
            {results.length > 0 && (
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">Sort by:</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'relevance')}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="relevance">Relevance</option>
                  <option value="date">Latest</option>
                </select>
              </div>
            )}

            {/* Active Filters Display */}
            {(query.search || query.category || query.tag) && (
              <div className="flex flex-wrap items-center gap-2">
                {query.search && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    Search: "{query.search}"
                    <button
                      onClick={() => window.location.href = '/search'}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {query.category && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    Category: {query.category}
                    <button
                      onClick={() => {
                        const params = new URLSearchParams(window.location.search);
                        params.delete('category');
                        window.location.href = `/search?${params.toString()}`;
                      }}
                      className="ml-2 text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {query.tag && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    Tag: {query.tag}
                    <button
                      onClick={() => {
                        const params = new URLSearchParams(window.location.search);
                        params.delete('tag');
                        window.location.href = `/search?${params.toString()}`;
                      }}
                      className="ml-2 text-purple-600 hover:text-purple-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                <button
                  onClick={() => window.location.href = '/search'}
                  className="text-sm text-gray-500 hover:text-gray-700 underline"
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <Card className="border-0 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <CardTitle className="text-lg font-bold text-gray-900 flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                    </svg>
                    Filters
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {filtersLoading ? (
                    <div className="space-y-4">
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="space-y-2">
                          {[...Array(3)].map((_, i) => (
                            <div key={i} className="h-8 bg-gray-100 rounded"></div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <PostFilters
                      categories={categories}
                      tags={tags}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            {loading ? (
              <div className="space-y-6">
                {/* Loading Skeleton */}
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-white rounded-lg shadow-md overflow-hidden">
                      <div className="h-48 bg-gray-200"></div>
                      <div className="p-6">
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div className="h-3 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <>
                {/* Results Grid */}
                {results.length > 0 ? (
                  <>
                    {/* Results Summary */}
                    <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-blue-800 font-medium">
                            Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                          </span>
                        </div>
                        <div className="text-sm text-blue-600">
                          Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                      {results.map((post, index) => (
                        <div key={post.id} className="group">
                          <PostCard
                            post={post}
                            author={post.author}
                            onPostDeleted={handlePostDeleted}
                          />
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {pagination.total > pagination.limit && (
                      <div className="mt-8">
                        <Pagination
                          currentPage={pagination.page}
                          totalPages={Math.ceil(pagination.total / pagination.limit)}
                          onPageChange={handlePageChange}
                          totalItems={pagination.total}
                          itemsPerPage={pagination.limit}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-16">
                    <div className="mb-6">
                      <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No results found</h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      {query.search || query.category || query.tag
                        ? "We couldn't find any posts matching your search criteria. Try adjusting your search terms or filters."
                        : "Start searching to discover amazing content!"
                      }
                    </p>

                    {(query.search || query.category || query.tag) ? (
                      <div className="space-y-3">
                        <p className="text-sm text-gray-500">Suggestions:</p>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Check your spelling</li>
                          <li>• Try different keywords</li>
                          <li>• Use more general terms</li>
                          <li>• Remove some filters</li>
                        </ul>
                      </div>
                    ) : (
                      <div className="mt-8">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Popular Categories</h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                          {categories.slice(0, 8).map((category) => (
                            <button
                              key={category.id}
                              onClick={() => window.location.href = `/search?category=${category.slug}`}
                              className="p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-left transition-colors group"
                            >
                              <div className="font-medium text-gray-900 group-hover:text-indigo-600">
                                {category.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {category.count} posts
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Back to Top Button */}
        {results.length > 5 && (
          <div className="mt-12 text-center">
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
              </svg>
              Back to Top
            </button>
          </div>
        )}
        </div>
      </div>
    </Layout>
  );
}
