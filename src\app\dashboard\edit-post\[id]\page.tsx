'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import Layout from '@/components/layout/layout';
import PostForm from '@/components/forms/post-form';

export default function EditPost() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user && params.id) {
      fetchPost();
    }
  }, [session, status, router, params.id]);

  const fetchPost = async () => {
    try {
      // Use the protected edit endpoint that includes permission checks
      const response = await fetch(`/api/posts/${params.id}/edit`);
      const result = await response.json();

      if (result.success) {
        setPost(result.data);
        setHasPermission(true);
      } else {
        setError(result.error || 'Failed to load post');
        setHasPermission(false);
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      setError('An error occurred while loading the post');
      setHasPermission(false);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="mb-6">
              <svg className="mx-auto h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">{error}</p>
            </div>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Dashboard
              </button>
              <button
                onClick={() => router.back()}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!session || !post) {
    return null;
  }

  // If user doesn't have permission, don't render the form
  if (!hasPermission) {
    return null;
  }

  return (
    <Layout className="bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <PostForm initialData={post} isEditing={true} />
    </Layout>
  );
}
