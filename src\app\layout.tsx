import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import AuthProvider from "@/components/providers/session-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";
import HydrationFix from "@/components/hydration-fix";
import Analytics from "@/components/seo/analytics";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Wikify - How to Guide you can Trust",
  description: "Wikify - How to Guide you can Trust. Your trusted source for comprehensive guides and tutorials.",
  keywords: "wikify, how to guide, tutorials, guides, trusted content",
  authors: [{ name: "Wikify Team" }],
  creator: "Wikify",
  publisher: "Wikify",
  applicationName: "Wikify",
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  openGraph: {
    title: "Wikify - How to Guide you can Trust",
    description: "Your trusted source for comprehensive guides and tutorials.",
    siteName: "Wikify",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Wikify - How to Guide you can Trust",
    description: "Your trusted source for comprehensive guides and tutorials.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <Analytics />
        <ThemeProvider defaultTheme="light">
          <AuthProvider>
            <HydrationFix />
            {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
