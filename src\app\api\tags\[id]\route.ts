import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils/slug';

// GET /api/tags/[id] - Get single tag
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      );
    }

    const result = await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, tagId))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'Tag not found' },
        { status: 404 }
      );
    }

    const { term, taxonomy } = result[0];

    return NextResponse.json({
      success: true,
      data: {
        id: term?.term_id,
        name: term?.name,
        slug: term?.slug,
        description: taxonomy?.description || '',
        count: taxonomy?.count || 0
      }
    });

  } catch (error) {
    console.error('Error fetching tag:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch tag',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/tags/[id] - Update tag
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const tagId = parseInt(id);
    const body = await request.json();
    const { name, slug, description } = body;

    if (isNaN(tagId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, tagId))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .limit(1);

    if (!existingTag.length) {
      return NextResponse.json(
        { success: false, error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Prepare update data for terms table
    const termUpdateData: any = {};
    if (name) termUpdateData.name = name;
    if (slug) {
      // Generate slug if needed
      termUpdateData.slug = slug || generateSlug(name);
    }

    // Prepare update data for taxonomy table
    const taxonomyUpdateData: any = {};
    if (description !== undefined) taxonomyUpdateData.description = description;

    // Update terms table
    if (Object.keys(termUpdateData).length > 0) {
      await db
        .update(terms)
        .set(termUpdateData)
        .where(eq(terms.term_id, tagId));
    }

    // Update taxonomy table
    if (Object.keys(taxonomyUpdateData).length > 0) {
      await db
        .update(term_taxonomy)
        .set(taxonomyUpdateData)
        .where(eq(term_taxonomy.term_id, tagId));
    }

    return NextResponse.json({
      success: true,
      data: { 
        id: tagId, 
        ...termUpdateData, 
        ...taxonomyUpdateData 
      }
    });

  } catch (error) {
    console.error('Error updating tag:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update tag',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/tags/[id] - Delete tag
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, tagId))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'))
      .limit(1);

    if (!existingTag.length) {
      return NextResponse.json(
        { success: false, error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Get taxonomy ID for relationships
    const taxonomyId = existingTag[0].taxonomy?.term_taxonomy_id;

    if (taxonomyId) {
      // Delete term relationships first
      await db
        .delete(term_relationships)
        .where(eq(term_relationships.term_taxonomy_id, taxonomyId));
    }

    // Delete taxonomy entry
    await db
      .delete(term_taxonomy)
      .where(eq(term_taxonomy.term_id, tagId));

    // Delete term
    await db
      .delete(terms)
      .where(eq(terms.term_id, tagId));

    return NextResponse.json({
      success: true,
      message: 'Tag deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting tag:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete tag',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
