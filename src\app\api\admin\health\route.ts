import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { options, posts, users } from '@/lib/db/schema';
import { eq, count } from 'drizzle-orm';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const healthChecks = [];

    // Database connectivity check
    try {
      await db.select().from(users).limit(1);
      healthChecks.push({
        name: 'Database Connection',
        status: 'healthy',
        message: 'Database is accessible'
      });
    } catch (error) {
      healthChecks.push({
        name: 'Database Connection',
        status: 'error',
        message: 'Database connection failed'
      });
    }

    // Settings check
    try {
      const settingsData = await db
        .select()
        .from(options)
        .where(eq(options.option_name, 'site_settings'));
      
      healthChecks.push({
        name: 'Site Settings',
        status: settingsData.length > 0 ? 'healthy' : 'warning',
        message: settingsData.length > 0 ? 'Settings configured' : 'No settings found'
      });
    } catch (error) {
      healthChecks.push({
        name: 'Site Settings',
        status: 'error',
        message: 'Failed to load settings'
      });
    }

    // Content check
    try {
      const postsCount = await db
        .select({ count: count() })
        .from(posts);
      
      const totalPosts = postsCount[0]?.count || 0;
      
      healthChecks.push({
        name: 'Content',
        status: totalPosts > 0 ? 'healthy' : 'warning',
        message: `${totalPosts} posts found`
      });
    } catch (error) {
      healthChecks.push({
        name: 'Content',
        status: 'error',
        message: 'Failed to check content'
      });
    }

    // Users check
    try {
      const usersCount = await db
        .select({ count: count() })
        .from(users);
      
      const totalUsers = usersCount[0]?.count || 0;
      
      healthChecks.push({
        name: 'Users',
        status: totalUsers > 0 ? 'healthy' : 'warning',
        message: `${totalUsers} users registered`
      });
    } catch (error) {
      healthChecks.push({
        name: 'Users',
        status: 'error',
        message: 'Failed to check users'
      });
    }

    // Environment checks
    const envChecks = [
      {
        name: 'Node.js Version',
        status: 'healthy',
        message: `Node.js ${process.version}`
      },
      {
        name: 'Environment',
        status: 'healthy',
        message: `Running in ${process.env.NODE_ENV || 'development'} mode`
      }
    ];

    healthChecks.push(...envChecks);

    // Overall health status
    const hasErrors = healthChecks.some(check => check.status === 'error');
    const hasWarnings = healthChecks.some(check => check.status === 'warning');
    
    const overallStatus = hasErrors ? 'error' : hasWarnings ? 'warning' : 'healthy';

    return NextResponse.json({
      success: true,
      data: {
        overall: overallStatus,
        timestamp: new Date().toISOString(),
        checks: healthChecks
      }
    });

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { error: 'Failed to perform health check' },
      { status: 500 }
    );
  }
}
