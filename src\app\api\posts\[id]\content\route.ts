import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params for Next.js 15 compatibility
    const { id } = await params;
    const postId = parseInt(id);
    const body = await request.json();
    const { content } = body;

    if (isNaN(postId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { success: false, error: 'Content is required' },
        { status: 400 }
      );
    }

    // Check if post exists and user has permission
    const existingPost = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!existingPost.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    const post = existingPost[0];

    // Check permissions
    const isOwner = post.post_author === parseInt(session.user.id);
    const isAdminOrEditor = ['ADMIN', 'EDITOR'].includes(session.user.role);

    if (!isOwner && !isAdminOrEditor) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Update post content
    const now = new Date();
    const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');

    await db
      .update(posts)
      .set({
        post_content: content,
        post_modified: mysqlDateTime,
        post_modified_gmt: mysqlDateTime
      })
      .where(eq(posts.ID, postId));

    return NextResponse.json({
      success: true,
      data: {
        id: postId,
        content,
        modified: mysqlDateTime
      }
    });

  } catch (error) {
    console.error('Error updating post content:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update post content',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
