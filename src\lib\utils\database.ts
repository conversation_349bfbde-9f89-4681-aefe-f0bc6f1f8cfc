import { db } from '@/lib/db';
import { users, posts, comments, terms, term_taxonomy, usermeta, postmeta, term_relationships } from '@/lib/db/schema';
import { eq, desc, and, like, count, sql } from 'drizzle-orm';
import { processWordPressContent, formatPostContent, extractFeaturedImage, generateExcerpt, getFeaturedImage } from './content-processor';

// Helper functions for WordPress data compatibility

export async function getUsers(limit = 10) {
  try {
    return await db.select().from(users).limit(limit);
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

export async function getUserByLogin(userLogin: string) {
  try {
    const result = await db
      .select()
      .from(users)
      .where(eq(users.user_login, userLogin))
      .limit(1);

    return result[0] || null;
  } catch (error) {
    console.error('Error fetching user by login:', error);
    return null;
  }
}

export async function getUserByEmail(email: string) {
  try {
    const result = await db
      .select()
      .from(users)
      .where(eq(users.user_email, email))
      .limit(1);

    return result[0] || null;
  } catch (error) {
    console.error('Error fetching user by email:', error);
    return null;
  }
}

export async function getUserMeta(userId: number, metaKey?: string) {
  try {
    let query = db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, userId));

    if (metaKey) {
      query = query.where(eq(usermeta.meta_key, metaKey));
    }

    return await query;
  } catch (error) {
    console.error('Error fetching user meta:', error);
    return [];
  }
}

export async function getUserRole(userId: number): Promise<string> {
  try {
    const capabilities = await db
      .select()
      .from(usermeta)
      .where(and(
        eq(usermeta.user_id, userId),
        eq(usermeta.meta_key, 'wikify1h_capabilities')
      ))
      .limit(1);

    if (capabilities.length > 0) {
      const caps = capabilities[0].meta_value || '';
      if (caps.includes('administrator')) return 'ADMIN';
      if (caps.includes('editor')) return 'EDITOR';
      if (caps.includes('author')) return 'AUTHOR';
      if (caps.includes('contributor')) return 'AUTHOR';
      if (caps.includes('subscriber')) return 'AUTHOR';
    }

    return 'AUTHOR'; // Default role
  } catch (error) {
    console.error('Error getting user role:', error);
    return 'AUTHOR';
  }
}

export async function getPosts(limit = 10) {
  try {
    return await db
      .select()
      .from(posts)
      .where(eq(posts.post_type, 'post'))
      .orderBy(desc(posts.post_date))
      .limit(limit);
  } catch (error) {
    console.error('Error fetching posts:', error);
    return [];
  }
}

export async function getPostBySlug(slug: string): Promise<any> {
  try {
    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(
        eq(posts.post_name, slug),
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'publish')
      ))
      .limit(1);

    if (!result.length) return null;

    const { post, author } = result[0];

    // Get ad codes from postmeta
    const adMeta = await db.execute(sql`
      SELECT meta_key, meta_value
      FROM wikify1h_postmeta
      WHERE post_id = ${post.ID}
      AND meta_key IN ('_before_content_ads', '_after_content_ads')
    `);

    // Extract the actual data from the result array
    const adMetaData = Array.isArray(adMeta[0]) ? adMeta[0] : adMeta;

    const beforeContentAds = adMetaData.find((meta: any) => meta.meta_key === '_before_content_ads')?.meta_value || '';
    const afterContentAds = adMetaData.find((meta: any) => meta.meta_key === '_after_content_ads')?.meta_value || '';

    return {
      post: {
        ...post,
        post_content: formatPostContent(post.post_content),
        post_excerpt: post.post_excerpt || generateExcerpt(post.post_content),
        featured_image: await getFeaturedImage(post.ID, post.post_content),
        beforeContentAds: beforeContentAds,
        afterContentAds: afterContentAds
      },
      author
    };
  } catch (error) {
    console.error('Error fetching post by slug:', error);
    return null;
  }
}

export async function getAuthorBySlug(slug: string): Promise<any> {
  try {
    const result = await db
      .select()
      .from(users)
      .where(eq(users.user_nicename, slug))
      .limit(1);

    if (!result.length) return null;

    const user = result[0];

    // Get author's bio from usermeta
    const bioResult = await db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, user.ID))
      .where(eq(usermeta.meta_key, 'description'))
      .limit(1);

    const bio = bioResult.length > 0 ? bioResult[0].meta_value : '';

    // Get author's profile image from usermeta (if exists)
    const profileImageResult = await db
      .select()
      .from(usermeta)
      .where(eq(usermeta.user_id, user.ID))
      .where(eq(usermeta.meta_key, 'profile_image'))
      .limit(1);

    const profileImage = profileImageResult.length > 0 ? profileImageResult[0].meta_value : null;

    return {
      id: user.ID,
      username: user.user_login,
      displayName: user.display_name || user.user_login,
      nicename: user.user_nicename,
      email: user.user_email,
      url: user.user_url,
      bio: bio || '',
      registered: user.user_registered,
      profileImage: profileImage
    };
  } catch (error) {
    console.error('Error fetching author by slug:', error);
    return null;
  }
}

export async function getPostsWithAuthor(limit = 10) {
  try {
    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'publish')
      ))
      .orderBy(desc(posts.post_date))
      .limit(limit);

    // Process content for each post
    return await Promise.all(
      result.map(async ({ post, author }) => ({
        post: {
          ...post,
          post_content: processWordPressContent(post.post_content),
          post_excerpt: post.post_excerpt || generateExcerpt(post.post_content),
          featured_image: await getFeaturedImage(post.ID, post.post_content)
        },
        author
      }))
    );
  } catch (error) {
    console.error('Error fetching posts with authors:', error);
    return [];
  }
}

export async function getPostsByAuthor(authorId: number, limit = 10) {
  try {
    return await db
      .select()
      .from(posts)
      .where(and(
        eq(posts.post_author, authorId),
        eq(posts.post_type, 'post')
      ))
      .orderBy(desc(posts.post_date))
      .limit(limit);
  } catch (error) {
    console.error('Error fetching posts by author:', error);
    return [];
  }
}

export async function getPostMeta(postId: number, metaKey?: string) {
  try {
    let query = db
      .select()
      .from(postmeta)
      .where(eq(postmeta.post_id, postId));

    if (metaKey) {
      query = query.where(eq(postmeta.meta_key, metaKey));
    }

    return await query;
  } catch (error) {
    console.error('Error fetching post meta:', error);
    return [];
  }
}

export async function getPostWithCategories(postId: number) {
  try {
    const post = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!post.length) return null;

    // Get categories for this post
    const categories = await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(term_relationships)
      .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(and(
        eq(term_relationships.object_id, postId),
        eq(term_taxonomy.taxonomy, 'category')
      ));

    // Get tags for this post
    const tags = await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(term_relationships)
      .leftJoin(term_taxonomy, eq(term_relationships.term_taxonomy_id, term_taxonomy.term_taxonomy_id))
      .leftJoin(terms, eq(term_taxonomy.term_id, terms.term_id))
      .where(and(
        eq(term_relationships.object_id, postId),
        eq(term_taxonomy.taxonomy, 'post_tag')
      ));

    return {
      post: post[0],
      categories,
      tags
    };
  } catch (error) {
    console.error('Error fetching post with categories:', error);
    return null;
  }
}

export async function getCategories() {
  try {
    return await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'category'));
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export async function getTags() {
  try {
    return await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(term_taxonomy.taxonomy, 'post_tag'));
  } catch (error) {
    console.error('Error fetching tags:', error);
    return [];
  }
}

export async function searchPosts(query: string, limit = 10) {
  try {
    return await db
      .select()
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'publish'),
        like(posts.post_title, `%${query}%`)
      ))
      .orderBy(desc(posts.post_date))
      .limit(limit);
  } catch (error) {
    console.error('Error searching posts:', error);
    return [];
  }
}
