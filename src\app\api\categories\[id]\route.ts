import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { terms, term_taxonomy, term_relationships } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/categories/[id] - Get single category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    const result = await db
      .select({
        term: terms,
        taxonomy: term_taxonomy
      })
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, categoryId))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      );
    }

    const { term, taxonomy } = result[0];

    return NextResponse.json({
      success: true,
      data: {
        id: term?.term_id,
        name: term?.name,
        slug: term?.slug,
        description: taxonomy?.description || '',
        count: taxonomy?.count || 0,
        parent: taxonomy?.parent || 0
      }
    });

  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch category',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/categories/[id] - Update category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const categoryId = parseInt(id);
    const body = await request.json();
    const { name, slug, description, parent } = body;

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, categoryId))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .limit(1);

    if (!existingCategory.length) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      );
    }

    // Prepare update data for terms table
    const termUpdateData: any = {};
    if (name) termUpdateData.name = name;
    if (slug) {
      // Generate slug if needed
      termUpdateData.slug = slug || name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }

    // Prepare update data for taxonomy table
    const taxonomyUpdateData: any = {};
    if (description !== undefined) taxonomyUpdateData.description = description;
    if (parent !== undefined) taxonomyUpdateData.parent = parent;

    // Update terms table
    if (Object.keys(termUpdateData).length > 0) {
      await db
        .update(terms)
        .set(termUpdateData)
        .where(eq(terms.term_id, categoryId));
    }

    // Update taxonomy table
    if (Object.keys(taxonomyUpdateData).length > 0) {
      await db
        .update(term_taxonomy)
        .set(taxonomyUpdateData)
        .where(eq(term_taxonomy.term_id, categoryId));
    }

    return NextResponse.json({
      success: true,
      data: { 
        id: categoryId, 
        ...termUpdateData, 
        ...taxonomyUpdateData 
      }
    });

  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update category',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/categories/[id] - Delete category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await db
      .select()
      .from(terms)
      .leftJoin(term_taxonomy, eq(terms.term_id, term_taxonomy.term_id))
      .where(eq(terms.term_id, categoryId))
      .where(eq(term_taxonomy.taxonomy, 'category'))
      .limit(1);

    if (!existingCategory.length) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      );
    }

    // Get taxonomy ID for relationships
    const taxonomyId = existingCategory[0].taxonomy?.term_taxonomy_id;

    if (taxonomyId) {
      // Delete term relationships first
      await db
        .delete(term_relationships)
        .where(eq(term_relationships.term_taxonomy_id, taxonomyId));
    }

    // Delete taxonomy entry
    await db
      .delete(term_taxonomy)
      .where(eq(term_taxonomy.term_id, categoryId));

    // Delete term
    await db
      .delete(terms)
      .where(eq(terms.term_id, categoryId));

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete category',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
