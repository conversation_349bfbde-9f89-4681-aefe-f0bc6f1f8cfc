'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';
import {
  Clock,
  Check,
  X,
  Trash2,
  Mail,
  Calendar,
  User,
  AlertCircle,
  RefreshCw,
  Search,
  Phone
} from 'lucide-react';

interface PendingUser {
  id: number;
  username: string;
  email: string;
  displayName: string;
  registered: string;
  status: string;
  mobile: string;
}

export default function PendingUsersPage() {
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<PendingUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<number | null>(null);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [adsPermissions, setAdsPermissions] = useState<Record<number, boolean>>({});
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
  const [selectedUserDetails, setSelectedUserDetails] = useState<PendingUser | null>(null);

  useEffect(() => {
    fetchPendingUsers();
  }, []);

  // Filter users based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(pendingUsers);
    } else {
      const filtered = pendingUsers.filter(user =>
        user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.displayName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [pendingUsers, searchQuery]);

  const fetchPendingUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/users/pending');
      const result = await response.json();

      if (result.success) {
        setPendingUsers(result.data);
        setFilteredUsers(result.data);
      } else {
        setError(result.error || 'Failed to fetch pending users');
      }
    } catch (error) {
      setError('An error occurred while fetching pending users');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (userId: number) => {
    try {
      setActionLoading(userId);
      const canInsertAds = adsPermissions[userId] || false;

      const response = await fetch(`/api/admin/users/${userId}/approve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ canInsertAds })
      });

      const result = await response.json();

      if (result.success) {
        // Remove user from pending list
        setPendingUsers(prev => prev.filter(user => user.id !== userId));
        setFilteredUsers(prev => prev.filter(user => user.id !== userId));
        // Clear ads permission state for this user
        setAdsPermissions(prev => {
          const newState = { ...prev };
          delete newState[userId];
          return newState;
        });
      } else {
        alert(result.error || 'Failed to approve user');
      }
    } catch (error) {
      alert('An error occurred while approving user');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (userId: number) => {
    if (!confirm('Are you sure you want to permanently delete this user? This action cannot be undone and will remove all user data from the database.')) {
      return;
    }

    try {
      setActionLoading(userId);
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        // Remove user from pending list
        setPendingUsers(prev => prev.filter(user => user.id !== userId));
        setFilteredUsers(prev => prev.filter(user => user.id !== userId));
      } else {
        alert(result.error || 'Failed to delete user');
      }
    } catch (error) {
      alert('An error occurred while deleting user');
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleShowUserDetails = (user: PendingUser) => {
    setSelectedUserDetails(user);
    setShowUserDetailsModal(true);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Pending User Approvals</h1>
            <p className="text-gray-600 mt-1">
              Review and approve new user registrations
            </p>
          </div>
          <Button 
            onClick={fetchPendingUsers}
            disabled={loading}
            variant="outline"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Search Bar */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search users by username, email, or display name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Stats Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Pending Approvals</p>
                  <p className="text-2xl font-bold text-gray-900">{pendingUsers.length}</p>
                </div>
              </div>
              {searchQuery && (
                <div className="text-right">
                  <p className="text-sm text-gray-600">Search Results</p>
                  <p className="text-xl font-semibold text-blue-600">{filteredUsers.length}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 text-red-800">
                <AlertCircle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pending Users List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>Pending Users</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-gray-600">Loading pending users...</span>
                </div>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                {searchQuery ? (
                  <>
                    <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
                    <p className="text-gray-600">
                      No users found matching "{searchQuery}". Try adjusting your search terms.
                    </p>
                    <Button
                      onClick={() => setSearchQuery('')}
                      variant="outline"
                      className="mt-4"
                    >
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Users</h3>
                    <p className="text-gray-600">All user registrations have been processed.</p>
                  </>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {searchQuery && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <p className="text-blue-800 text-sm">
                      Showing {filteredUsers.length} of {pendingUsers.length} pending users
                      {searchQuery && ` matching "${searchQuery}"`}
                    </p>
                  </div>
                )}
                {filteredUsers.map((user) => (
                  <div 
                    key={user.id} 
                    className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                            {user.username.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <h3
                              className="font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                              onClick={() => handleShowUserDetails(user)}
                            >
                              {user.displayName || user.username}
                            </h3>
                            <p className="text-sm text-gray-600">@{user.username}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-2">
                            <Mail className="w-4 h-4" />
                            <span>{user.email}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Phone className="w-4 h-4" />
                            <span>{user.mobile || 'Not provided'}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4" />
                            <span>Registered: {formatDate(user.registered)}</span>
                          </div>
                        </div>

                        {/* Ads Permission Checkbox */}
                        <div className="mt-3 flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`ads-permission-${user.id}`}
                            checked={adsPermissions[user.id] || false}
                            onChange={(e) => setAdsPermissions(prev => ({
                              ...prev,
                              [user.id]: e.target.checked
                            }))}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                          <label
                            htmlFor={`ads-permission-${user.id}`}
                            className="text-sm text-gray-700 font-medium"
                          >
                            Grant Ads Insert Permission
                          </label>
                          <span className="text-xs text-gray-500">
                            (Allow user to monetize content with advertisements)
                          </span>
                        </div>
                      </div>

                      <div className="flex flex-col items-end space-y-2 ml-4">
                        <Button
                          onClick={() => handleApprove(user.id)}
                          disabled={actionLoading === user.id}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          {actionLoading === user.id ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <>
                              <Check className="w-4 h-4 mr-1" />
                              Approve
                            </>
                          )}
                        </Button>
                        
                        <Button
                          onClick={() => handleDelete(user.id)}
                          disabled={actionLoading === user.id}
                          size="sm"
                          variant="outline"
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          {actionLoading === user.id ? (
                            <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <>
                              <Trash2 className="w-4 h-4 mr-1" />
                              Delete
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Details Modal */}
        <Modal
          isOpen={showUserDetailsModal}
          onClose={() => setShowUserDetailsModal(false)}
          title="User Details"
        >
          {selectedUserDetails && (
            <div className="space-y-6">
              {/* User Avatar and Basic Info */}
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {selectedUserDetails.username.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    {selectedUserDetails.displayName || selectedUserDetails.username}
                  </h2>
                  <p className="text-gray-600">@{selectedUserDetails.username}</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending Approval
                  </span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Email</p>
                      <p className="text-gray-900">{selectedUserDetails.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Mobile Number</p>
                      <p className="text-gray-900">{selectedUserDetails.mobile || 'Not provided'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Registration Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Registration Information</h3>
                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Registration Date</p>
                    <p className="text-gray-900">{formatDate(selectedUserDetails.registered)}</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="border-t pt-6 flex space-x-3">
                <Button
                  onClick={() => {
                    handleApprove(selectedUserDetails.id);
                    setShowUserDetailsModal(false);
                  }}
                  disabled={actionLoading === selectedUserDetails.id}
                  className="flex-1"
                >
                  <Check className="w-4 h-4 mr-2" />
                  Approve User
                </Button>
                <Button
                  onClick={() => {
                    handleDelete(selectedUserDetails.id);
                    setShowUserDetailsModal(false);
                  }}
                  disabled={actionLoading === selectedUserDetails.id}
                  variant="destructive"
                  className="flex-1"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete User
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </AdminLayout>
  );
}
