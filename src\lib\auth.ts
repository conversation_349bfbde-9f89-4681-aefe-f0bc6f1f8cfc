import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { getUserByEmail, getUserByLogin, getUserRole } from '@/lib/utils/database';
import { verifyWordPressPassword } from '@/lib/utils/wordpress-auth';
import { canUserInsertAds } from '@/lib/utils/ads-permissions';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        login: { label: 'Username or Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.login || !credentials?.password) {
          return null;
        }

        try {
          // Find user by email or username
          let foundUser = null;

          if (credentials.login.includes('@')) {
            foundUser = await getUserByEmail(credentials.login);
          } else {
            foundUser = await getUserByLogin(credentials.login);
          }

          // If not found by username, try email
          if (!foundUser && !credentials.login.includes('@')) {
            foundUser = await getUserByEmail(credentials.login);
          }

          if (!foundUser) {
            return null;
          }

          // Verify password using WordPress-compatible verification
          const isPasswordValid = verifyWordPressPassword(credentials.password, foundUser.user_pass);

          if (!isPasswordValid) {
            return null;
          }

          // Get user role
          const userRole = await getUserRole(foundUser.ID);

          // Get ads permission
          const adsPermission = await canUserInsertAds(foundUser.ID);

          // Return user object with minimal required fields
          return {
            id: foundUser.ID.toString(),
            email: foundUser.user_email,
            name: foundUser.display_name || foundUser.user_login,
            username: foundUser.user_login,
            role: userRole,
            status: foundUser.user_status,
            canInsertAds: adsPermission,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.username = user.username;
        token.status = user.status;
        token.canInsertAds = user.canInsertAds;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && token.sub) {
        session.user.id = token.sub;
        session.user.role = token.role as string;
        session.user.username = token.username as string;
        session.user.status = token.status as number;

        // Fetch current ads permission from database
        try {
          const userId = parseInt(token.sub);
          if (!isNaN(userId)) {
            session.user.canInsertAds = await canUserInsertAds(userId);
          } else {
            session.user.canInsertAds = false;
          }
        } catch (error) {
          console.error('Error fetching ads permission:', error);
          session.user.canInsertAds = false;
        }
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production'
        ? '__Secure-next-auth.session-token'
        : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production'
          ? '.wikify.xyz'
          : undefined,
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production'
        ? '__Secure-next-auth.callback-url'
        : 'next-auth.callback-url',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production'
          ? '.wikify.xyz'
          : undefined,
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production'
        ? '__Host-next-auth.csrf-token'
        : 'next-auth.csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  debug: process.env.NODE_ENV === 'development',
};




