import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'data', 'settings.json');

export async function GET() {
  try {
    // Check if file exists
    try {
      await fs.access(SETTINGS_FILE);
      const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
      const settings = JSON.parse(data);
      
      return NextResponse.json({
        success: true,
        source: 'file',
        path: SETTINGS_FILE,
        data: settings
      });
    } catch (fileError) {
      return NextResponse.json({
        success: false,
        source: 'file',
        path: SETTINGS_FILE,
        error: 'File not found or invalid JSON',
        details: fileError instanceof Error ? fileError.message : 'Unknown error'
      });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }
    
    // Save settings to file
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(body, null, 2));
    
    return NextResponse.json({
      success: true,
      message: 'Settings saved to file',
      path: SETTINGS_FILE
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to save settings',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
