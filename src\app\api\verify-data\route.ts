import { NextRequest, NextResponse } from 'next/server';
import { getUsers, getPosts, getCategories, getTags } from '@/lib/utils/database';
import { db } from '@/lib/db';
import { users, posts, comments } from '@/lib/db/schema';
import { count } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    // Get counts of existing data
    const [userCount] = await db.select({ count: count() }).from(users);
    const [postCount] = await db.select({ count: count() }).from(posts);
    const [commentCount] = await db.select({ count: count() }).from(comments);

    // Get sample data
    const sampleUsers = await getUsers(5);
    const samplePosts = await getPosts(5);
    const categories = await getCategories();
    const tags = await getTags();

    return NextResponse.json({
      success: true,
      data: {
        counts: {
          users: userCount.count,
          posts: postCount.count,
          comments: commentCount.count,
          categories: categories.length,
          tags: tags.length
        },
        samples: {
          users: sampleUsers.map(user => ({
            id: user.ID,
            username: user.user_login,
            email: user.user_email,
            displayName: user.display_name,
            registered: user.user_registered
          })),
          posts: samplePosts.map(post => ({
            id: post.ID,
            title: post.post_title,
            slug: post.post_name,
            status: post.post_status,
            type: post.post_type,
            date: post.post_date,
            author: post.post_author
          })),
          categories: categories.slice(0, 5).map(cat => ({
            id: cat.term?.term_id,
            name: cat.term?.name,
            slug: cat.term?.slug,
            count: cat.taxonomy?.count
          })),
          tags: tags.slice(0, 5).map(tag => ({
            id: tag.term?.term_id,
            name: tag.term?.name,
            slug: tag.term?.slug,
            count: tag.taxonomy?.count
          }))
        }
      }
    });

  } catch (error) {
    console.error('Error verifying data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to verify data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
