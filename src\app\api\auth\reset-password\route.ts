import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, passwordResetTokens } from '@/lib/db/schema';
import { eq, and, gt } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    const { token, password, confirmPassword } = await request.json();

    // Validate input
    if (!token || !password || !confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Check if passwords match
    if (password !== confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'Passwords do not match' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Find valid reset token
    const now = new Date();
    const resetTokenRecord = await db
      .select()
      .from(passwordResetTokens)
      .where(
        and(
          eq(passwordResetTokens.token, token),
          eq(passwordResetTokens.used, false),
          gt(passwordResetTokens.expires, now)
        )
      )
      .limit(1);

    if (resetTokenRecord.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired reset token. Please request a new password reset.' },
        { status: 400 }
      );
    }

    const tokenData = resetTokenRecord[0];

    // Find user by email
    const userRecord = await db
      .select({
        ID: users.ID,
        user_login: users.user_login,
        user_email: users.user_email,
        user_status: users.user_status
      })
      .from(users)
      .where(eq(users.user_email, tokenData.email))
      .limit(1);

    if (userRecord.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const user = userRecord[0];

    // Check if user account is approved
    if (user.user_status !== 1) { // 1 = APPROVED
      return NextResponse.json(
        { success: false, error: 'Account is not approved. Please contact support.' },
        { status: 403 }
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update user password
    await db
      .update(users)
      .set({
        user_pass: hashedPassword,
        // Clear activation key if it exists
        user_activation_key: ''
      })
      .where(eq(users.ID, user.ID));

    // Mark the reset token as used
    await db
      .update(passwordResetTokens)
      .set({ used: true })
      .where(eq(passwordResetTokens.id, tokenData.id));

    // Optional: Clean up old/expired tokens for this email
    await db
      .delete(passwordResetTokens)
      .where(
        and(
          eq(passwordResetTokens.email, tokenData.email),
          eq(passwordResetTokens.used, false),
          gt(now, passwordResetTokens.expires)
        )
      );

    console.log('Password reset successful for user:', user.user_login);

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully. You can now sign in with your new password.'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to verify token validity
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    // Check if token is valid and not expired
    const now = new Date();
    const resetTokenRecord = await db
      .select({
        id: passwordResetTokens.id,
        email: passwordResetTokens.email,
        expires: passwordResetTokens.expires,
        used: passwordResetTokens.used
      })
      .from(passwordResetTokens)
      .where(
        and(
          eq(passwordResetTokens.token, token),
          eq(passwordResetTokens.used, false),
          gt(passwordResetTokens.expires, now)
        )
      )
      .limit(1);

    if (resetTokenRecord.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 400 }
      );
    }

    const tokenData = resetTokenRecord[0];

    return NextResponse.json({
      success: true,
      data: {
        email: tokenData.email,
        expires: tokenData.expires,
        valid: true
      }
    });

  } catch (error) {
    console.error('Token verification error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred while verifying token.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
