import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import { USER_STATUS } from '@/lib/db/schema';

export default withAuth(
  async function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // Skip middleware for API routes, static files, and auth routes
    if (
      pathname.startsWith('/api') ||
      pathname.startsWith('/_next') ||
      pathname.startsWith('/auth') ||
      pathname === '/maintenance' ||
      pathname.startsWith('/favicon')
    ) {
      return NextResponse.next();
    }

    // Redirect pending users trying to access dashboard/admin to pending page
    if ((pathname.startsWith('/dashboard') || pathname.startsWith('/admin')) &&
        token && token.status === USER_STATUS.PENDING) {
      return NextResponse.redirect(new URL('/pending', req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Allow access to public routes
        if (
          pathname.startsWith('/login') ||
          pathname.startsWith('/signup') ||
          pathname.startsWith('/forgot-password') ||
          pathname.startsWith('/api/auth') ||
          pathname === '/' ||
          pathname.startsWith('/blog') ||
          pathname.startsWith('/post') ||
          pathname.startsWith('/category') ||
          pathname.startsWith('/tag') ||
          pathname.startsWith('/author') ||
          pathname.startsWith('/_next') ||
          pathname.startsWith('/favicon') ||
          pathname === '/maintenance'
        ) {
          return true;
        }

        // Pending page requires authentication but allows pending users
        if (pathname === '/pending') {
          return !!token;
        }

        // Require authentication for protected routes
        if (pathname.startsWith('/dashboard') || pathname.startsWith('/admin')) {
          // Must have a valid token
          if (!token || !token.sub) {
            return false;
          }

          // Admin routes require admin or editor role and approved status
          if (pathname.startsWith('/admin')) {
            const hasRole = token.role === 'ADMIN' || token.role === 'EDITOR';
            const isApproved = token.status === USER_STATUS.APPROVED;
            return hasRole && isApproved;
          }

          // Dashboard requires approved status
          return token.status === USER_STATUS.APPROVED;
        }

        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
