/* Enhanced Post View Styles */

/* Reading Experience Enhancements */
.post-content {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000000 !important; /* Force black color for all post content */
}

.post-content p,
.post-content div,
.post-content span,
.post-content li {
  color: #000000 !important; /* Force black color for text elements */
}

/* Enhanced Typography */
.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  scroll-margin-top: 2rem;
  color: #000000 !important;
}

.post-content h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.post-content h2 {
  font-size: 2rem;
  color: #000000 !important;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.post-content h3 {
  font-size: 1.5rem;
  color: #000000 !important;
}

/* Enhanced Paragraphs */
.post-content p {
  font-size: 1.125rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: #000000 !important;
  text-align: justify;
  hyphens: auto;
}

.post-content p:first-of-type {
  font-size: 1.25rem;
  font-weight: 400;
  color: #000000 !important;
}

/* Enhanced Links */
.post-content a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.post-content a:hover {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

.post-content a::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.post-content a:hover::before {
  width: 100%;
}

/* Enhanced Lists */
.post-content ul,
.post-content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.post-content li {
  margin-bottom: 0.75rem;
  line-height: 1.7;
  color: #000000 !important;
}

.post-content ul li {
  position: relative;
}

.post-content ul li::marker {
  color: #3b82f6;
  font-size: 1.2em;
}

/* Enhanced Blockquotes */
.post-content blockquote {
  position: relative;
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border-left: 4px solid #3b82f6;
  border-radius: 0 1rem 1rem 0;
  font-style: italic;
  font-size: 1.1rem;
  color: #000000 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.post-content blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 4rem;
  color: #3b82f6;
  opacity: 0.3;
  font-family: serif;
}

/* Enhanced Code Blocks */
.post-content pre {
  background: #1f2937;
  color: #f9fafb;
  border-radius: 1rem;
  padding: 1.5rem;
  margin: 2rem 0;
  overflow-x: auto;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.post-content pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ef4444);
  border-radius: 1rem 1rem 0 0;
}

.post-content code {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.9em;
}

.post-content :not(pre) > code {
  background: #fef3c7;
  color: #92400e;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  border: 1px solid #fbbf24;
}

/* Enhanced Images */
.post-content img {
  border-radius: 1rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  margin: 2rem auto;
  display: block;
  max-width: 100%;
  height: auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-content img:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* Enhanced Tables */
.post-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.post-content th {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
}

.post-content td {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.post-content tr:nth-child(even) {
  background: #f9fafb;
}

.post-content tr:hover {
  background: #eff6ff;
}

/* Enhanced HR */
.post-content hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  margin: 3rem 0;
  border-radius: 1px;
}

/* Reading Progress Indicator */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(59, 130, 246, 0.1);
  z-index: 1000;
}

.reading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  width: 0%;
  transition: width 0.1s ease;
}

/* Print Styles */
@media print {
  .post-content {
    font-size: 12pt;
    line-height: 1.6;
    color: black;
  }
  
  .post-content h1,
  .post-content h2,
  .post-content h3 {
    page-break-after: avoid;
    color: black;
  }
  
  .post-content img {
    max-width: 100%;
    page-break-inside: avoid;
  }
  
  .post-content blockquote {
    page-break-inside: avoid;
    border-left: 2px solid black;
    background: none;
  }
  
  .post-content pre {
    page-break-inside: avoid;
    background: #f5f5f5;
    color: black;
    border: 1px solid #ccc;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .post-content *,
  .post-content *::before,
  .post-content *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .post-content {
    color: black;
  }
  
  .post-content a {
    color: #0000EE;
    text-decoration: underline;
  }
  
  .post-content blockquote {
    background: white;
    border: 2px solid black;
  }
}

/* Theme-aware post content styles */
.post-content {
  color: var(--foreground);
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  color: var(--foreground);
}

.post-content p {
  color: var(--muted-foreground);
}

.post-content blockquote {
  background: var(--accent);
  color: var(--accent-foreground);
  border-left: 4px solid var(--primary);
}

.post-content a {
  color: var(--primary);
}

.post-content a:hover {
  color: var(--primary);
  opacity: 0.8;
}

.post-content code {
  background: var(--muted);
  color: var(--muted-foreground);
  border: 1px solid var(--border);
}

.post-content pre {
  background: var(--muted);
  border: 1px solid var(--border);
}

.post-content table {
  border-color: var(--border);
}

.post-content th,
.post-content td {
  border-color: var(--border);
}

.post-content th {
  background: var(--muted);
}

/* Responsive Design Enhancements */

/* Mobile Devices (320px - 768px) */
@media (max-width: 768px) {
  .post-content h1 {
    font-size: 1.875rem;
    line-height: 1.3;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }

  .post-content h2 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .post-content h3 {
    font-size: 1.25rem;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .post-content p {
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 1.25rem;
    text-align: left;
  }

  .post-content p:first-of-type {
    font-size: 1.125rem;
  }

  .post-content blockquote {
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }

  .post-content pre {
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0.75rem;
    font-size: 0.875rem;
  }

  .post-content img {
    margin: 1.5rem auto;
    border-radius: 0.75rem;
  }

  .post-content table {
    font-size: 0.875rem;
    margin: 1.5rem 0;
  }

  .post-content th,
  .post-content td {
    padding: 0.75rem 0.5rem;
  }

  .post-content ul,
  .post-content ol {
    padding-left: 1.5rem;
    margin: 1.25rem 0;
  }

  .post-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }
}

/* Tablet Devices (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .post-content h1 {
    font-size: 2.25rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .post-content h2 {
    font-size: 1.875rem;
    margin-top: 1.75rem;
    margin-bottom: 0.875rem;
  }

  .post-content h3 {
    font-size: 1.375rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .post-content p {
    font-size: 1.0625rem;
    line-height: 1.75;
    margin-bottom: 1.375rem;
  }

  .post-content p:first-of-type {
    font-size: 1.1875rem;
  }

  .post-content blockquote {
    margin: 1.75rem 0;
    padding: 1.25rem 1.75rem;
    font-size: 1.0625rem;
  }

  .post-content pre {
    padding: 1.25rem;
    margin: 1.75rem 0;
    border-radius: 0.875rem;
  }

  .post-content img {
    margin: 1.75rem auto;
    border-radius: 0.875rem;
  }

  .post-content table {
    margin: 1.75rem 0;
  }

  .post-content th,
  .post-content td {
    padding: 0.875rem 0.75rem;
  }
}

/* Large Screens (1024px+) */
@media (min-width: 1024px) {
  .post-content {
    font-size: 1.125rem;
  }

  .post-content h1 {
    font-size: 2.5rem;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
  }

  .post-content h2 {
    font-size: 2rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .post-content h3 {
    font-size: 1.5rem;
    margin-top: 1.75rem;
    margin-bottom: 0.875rem;
  }

  .post-content p {
    font-size: 1.125rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
  }

  .post-content p:first-of-type {
    font-size: 1.25rem;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .post-content a {
    padding: 0.25rem 0;
    margin: -0.25rem 0;
  }

  .post-content img:hover {
    transform: none;
  }

  .post-content tr:hover {
    background: inherit;
  }
}

/* Landscape Orientation on Mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .post-content h1 {
    font-size: 1.75rem;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .post-content h2 {
    font-size: 1.375rem;
    margin-top: 1.25rem;
    margin-bottom: 0.625rem;
  }

  .post-content p {
    margin-bottom: 1rem;
  }

  .post-content blockquote {
    margin: 1.25rem 0;
    padding: 0.875rem 1.25rem;
  }

  .post-content pre {
    margin: 1.25rem 0;
    padding: 0.875rem;
  }

  .post-content img {
    margin: 1.25rem auto;
  }
}

/* Universal Ad Container Styles */
.universal-ad-container {
  margin: 2rem auto;
  text-align: center;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.universal-ad-container * {
  max-width: 100% !important;
  box-sizing: border-box;
}

.universal-ad-container img {
  height: auto !important;
  width: auto !important;
  max-width: 100% !important;
  display: block;
  margin: 0 auto;
  text-align: center;
}

.universal-ad-container iframe {
  max-width: 100% !important;
  border: none;
}

/* Responsive ad wrapper for maintaining aspect ratios */
.responsive-ad-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  overflow: hidden;
}

/* AdSense responsive styles */
.universal-ad-container .adsbygoogle {
  display: block !important;
  width: 100% !important;
  height: auto !important;
  margin: 0 auto !important;
  text-align: center !important;
}

/* Advertica responsive styles */
.universal-ad-container ins[data-domain] {
  display: block !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  text-align: center !important;
}

/* Media.net responsive styles */
.universal-ad-container [data-type="ad"] {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  text-align: center !important;
}

/* Generic div and span elements in ads */
.universal-ad-container div,
.universal-ad-container span {
  max-width: 100% !important;
  text-align: center !important;
}

/* Ad container wrapper styles */
.ad-container {
  margin: 2rem auto;
  padding: 1rem;
  text-align: center;
  border-radius: 0.5rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 100%;
}

.ad-container.before-content-ads {
  margin-bottom: 2rem;
  margin-top: 0;
  margin-left: auto;
  margin-right: auto;
}

.ad-container.after-content-ads {
  margin-top: 2rem;
  margin-bottom: 0;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .universal-ad-container {
    margin: 1.5rem 0;
  }

  .universal-ad-container ins,
  .universal-ad-container div,
  .universal-ad-container iframe {
    max-width: 100% !important;
    width: 100% !important;
  }

  .ad-container {
    margin: 1.5rem 0;
    padding: 0.75rem;
    border-radius: 0.375rem;
  }

  .ad-container.before-content-ads {
    margin-bottom: 1.5rem;
    margin-top: 0;
  }

  .ad-container.after-content-ads {
    margin-top: 1.5rem;
    margin-bottom: 0;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .universal-ad-container {
    margin: 1rem 0;
  }

  .ad-container {
    margin: 1rem 0;
    padding: 0.5rem;
  }
}

/* Ensure ad scripts execute properly */
.universal-ad-container script {
  display: block;
}

/* Force responsive behavior for stubborn ads */
.universal-ad-container > * {
  max-width: 100% !important;
}

/* Hide overflow to prevent horizontal scrolling */
.universal-ad-container {
  overflow-x: hidden;
}
