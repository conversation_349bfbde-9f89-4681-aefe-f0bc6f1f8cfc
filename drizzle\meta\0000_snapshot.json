{"version": "5", "dialect": "mysql", "id": "a2f89fae-f669-43e9-8d04-ab7ac1e70d25", "prevId": "********-0000-0000-0000-************", "tables": {"accounts": {"name": "accounts", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_idx": {"name": "user_idx", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_comments": {"name": "wikify1h_comments", "columns": {"comment_ID": {"name": "comment_ID", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "comment_post_ID": {"name": "comment_post_ID", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "comment_author": {"name": "comment_author", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment_author_email": {"name": "comment_author_email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "comment_author_url": {"name": "comment_author_url", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "comment_author_IP": {"name": "comment_author_IP", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "comment_date": {"name": "comment_date", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment_date_gmt": {"name": "comment_date_gmt", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment_content": {"name": "comment_content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment_karma": {"name": "comment_karma", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "comment_approved": {"name": "comment_approved", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'1'"}, "comment_agent": {"name": "comment_agent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "comment_type": {"name": "comment_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'comment'"}, "comment_parent": {"name": "comment_parent", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "user_id": {"name": "user_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"comment_post_ID": {"name": "comment_post_ID", "columns": ["comment_post_ID"], "isUnique": false}, "comment_approved_date_gmt": {"name": "comment_approved_date_gmt", "columns": ["comment_approved", "comment_date_gmt"], "isUnique": false}, "comment_date_gmt": {"name": "comment_date_gmt", "columns": ["comment_date_gmt"], "isUnique": false}, "comment_parent": {"name": "comment_parent", "columns": ["comment_parent"], "isUnique": false}, "comment_author_email": {"name": "comment_author_email", "columns": ["comment_author_email"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_comments_comment_ID": {"name": "wikify1h_comments_comment_ID", "columns": ["comment_ID"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_options": {"name": "wikify1h_options", "columns": {"option_id": {"name": "option_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "option_name": {"name": "option_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "option_value": {"name": "option_value", "type": "longtext", "primaryKey": false, "notNull": true, "autoincrement": false}, "autoload": {"name": "autoload", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'yes'"}}, "indexes": {"option_name": {"name": "option_name", "columns": ["option_name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_options_option_id": {"name": "wikify1h_options_option_id", "columns": ["option_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "password_reset_tokens": {"name": "password_reset_tokens", "columns": {"id": {"name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "used": {"name": "used", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"email_idx": {"name": "email_idx", "columns": ["email"], "isUnique": false}, "token_idx": {"name": "token_idx", "columns": ["token"], "isUnique": false}, "expires_idx": {"name": "expires_idx", "columns": ["expires"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"password_reset_tokens_id": {"name": "password_reset_tokens_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_postmeta": {"name": "wikify1h_postmeta", "columns": {"meta_id": {"name": "meta_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "post_id": {"name": "post_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "meta_key": {"name": "meta_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "meta_value": {"name": "meta_value", "type": "longtext", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"post_id": {"name": "post_id", "columns": ["post_id"], "isUnique": false}, "meta_key": {"name": "meta_key", "columns": ["meta_key"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_postmeta_meta_id": {"name": "wikify1h_postmeta_meta_id", "columns": ["meta_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_posts": {"name": "wikify1h_posts", "columns": {"ID": {"name": "ID", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "post_author": {"name": "post_author", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "post_date": {"name": "post_date", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_date_gmt": {"name": "post_date_gmt", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_content": {"name": "post_content", "type": "longtext", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_title": {"name": "post_title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_excerpt": {"name": "post_excerpt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_status": {"name": "post_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'publish'"}, "comment_status": {"name": "comment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'open'"}, "ping_status": {"name": "ping_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'open'"}, "post_password": {"name": "post_password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "post_name": {"name": "post_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "to_ping": {"name": "to_ping", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pinged": {"name": "pinged", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_modified": {"name": "post_modified", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_modified_gmt": {"name": "post_modified_gmt", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_content_filtered": {"name": "post_content_filtered", "type": "longtext", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_parent": {"name": "post_parent", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "guid": {"name": "guid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "menu_order": {"name": "menu_order", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "post_type": {"name": "post_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'post'"}, "post_mime_type": {"name": "post_mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "comment_count": {"name": "comment_count", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"post_name": {"name": "post_name", "columns": ["post_name"], "isUnique": false}, "type_status_date": {"name": "type_status_date", "columns": ["post_type", "post_status", "post_date", "ID"], "isUnique": false}, "post_parent": {"name": "post_parent", "columns": ["post_parent"], "isUnique": false}, "post_author": {"name": "post_author", "columns": ["post_author"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_posts_ID": {"name": "wikify1h_posts_ID", "columns": ["ID"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sessions": {"name": "sessions", "columns": {"session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_idx": {"name": "user_idx", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"sessions_session_token": {"name": "sessions_session_token", "columns": ["session_token"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_term_relationships": {"name": "wikify1h_term_relationships", "columns": {"object_id": {"name": "object_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "term_taxonomy_id": {"name": "term_taxonomy_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "term_order": {"name": "term_order", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"term_taxonomy_id": {"name": "term_taxonomy_id", "columns": ["term_taxonomy_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_term_relationships_object_id_term_taxonomy_id_pk": {"name": "wikify1h_term_relationships_object_id_term_taxonomy_id_pk", "columns": ["object_id", "term_taxonomy_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_term_taxonomy": {"name": "wikify1h_term_taxonomy", "columns": {"term_taxonomy_id": {"name": "term_taxonomy_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "term_id": {"name": "term_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "taxonomy": {"name": "taxonomy", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "description": {"name": "description", "type": "longtext", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent": {"name": "parent", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "count": {"name": "count", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"term_id_taxonomy": {"name": "term_id_taxonomy", "columns": ["term_id", "taxonomy"], "isUnique": false}, "taxonomy": {"name": "taxonomy", "columns": ["taxonomy"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_term_taxonomy_term_taxonomy_id": {"name": "wikify1h_term_taxonomy_term_taxonomy_id", "columns": ["term_taxonomy_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_terms": {"name": "wikify1h_terms", "columns": {"term_id": {"name": "term_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "term_group": {"name": "term_group", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"slug": {"name": "slug", "columns": ["slug"], "isUnique": false}, "name": {"name": "name", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_terms_term_id": {"name": "wikify1h_terms_term_id", "columns": ["term_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_usermeta": {"name": "wikify1h_usermeta", "columns": {"umeta_id": {"name": "umeta_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "meta_key": {"name": "meta_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "meta_value": {"name": "meta_value", "type": "longtext", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_id": {"name": "user_id", "columns": ["user_id"], "isUnique": false}, "meta_key": {"name": "meta_key", "columns": ["meta_key"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_usermeta_umeta_id": {"name": "wikify1h_usermeta_umeta_id", "columns": ["umeta_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "wikify1h_users": {"name": "wikify1h_users", "columns": {"ID": {"name": "ID", "type": "bigint unsigned", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_login": {"name": "user_login", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_pass": {"name": "user_pass", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_nicename": {"name": "user_nicename", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_url": {"name": "user_url", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_registered": {"name": "user_registered", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_activation_key": {"name": "user_activation_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_status": {"name": "user_status", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}}, "indexes": {"user_login_key": {"name": "user_login_key", "columns": ["user_login"], "isUnique": false}, "user_nicename": {"name": "user_nicename", "columns": ["user_nicename"], "isUnique": false}, "user_email": {"name": "user_email", "columns": ["user_email"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"wikify1h_users_ID": {"name": "wikify1h_users_ID", "columns": ["ID"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "verification_tokens": {"name": "verification_tokens", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}