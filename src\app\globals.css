@import "tailwindcss";
@import "../styles/admin.css";

/* Hide scrollbar for horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Ad Container Styles */
.ad-container {
  margin: 20px 0;
  padding: 15px;
  border: 2px dashed var(--border);
  border-radius: 8px;
  background-color: var(--muted);
  color: var(--muted-foreground);
  text-align: center;
  position: relative;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.ad-container:hover {
  border-color: var(--primary);
  background-color: var(--accent);
}

/* Ad Container in published posts */
.post-content .ad-container {
  border: 1px solid var(--border);
  background-color: var(--card);
  color: var(--card-foreground);
}

/* Light Theme (Default) */
:root {
  --background: #ffffff;
  --foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f8fafc;
  --secondary-foreground: #475569;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;

  /* Additional light theme variables */
  --sidebar-bg: #f8fafc;
  --sidebar-border: #e2e8f0;
  --sidebar-text: #1e293b;
  --sidebar-text-muted: #475569;
  --sidebar-hover: #e2e8f0;
  --sidebar-active: #dbeafe;
  --sidebar-active-text: #1d4ed8;

  --header-bg: #f8fafc;
  --header-border: #e2e8f0;
  --header-text: #0f172a;

  --content-bg: #f1f5f9;
  --content-card: #f8fafc;
  --content-border: #cbd5e1;

  --button-secondary: #f3f4f6;
  --button-secondary-hover: #e5e7eb;
  --button-secondary-text: #374151;

  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus: #3b82f6;

  --toast-bg: #ffffff;
  --toast-border: #e5e7eb;
  --toast-shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
  --background: #0f172a;
  --foreground: #f8fafc;
  --primary: #60a5fa;
  --primary-foreground: #0f172a;
  --secondary: #1e293b;
  --secondary-foreground: #cbd5e1;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #1e293b;
  --accent-foreground: #f8fafc;
  --border: #334155;
  --input: #1e293b;
  --ring: #60a5fa;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --destructive: #f87171;
  --destructive-foreground: #0f172a;
  --success: #34d399;
  --success-foreground: #0f172a;
  --warning: #fbbf24;
  --warning-foreground: #0f172a;

  /* Additional dark theme variables */
  --sidebar-bg: #1e293b;
  --sidebar-border: #334155;
  --sidebar-text: #cbd5e1;
  --sidebar-text-muted: #94a3b8;
  --sidebar-hover: #334155;
  --sidebar-active: #1e40af;
  --sidebar-active-text: #93c5fd;

  --header-bg: #1e293b;
  --header-border: #334155;
  --header-text: #f8fafc;

  --content-bg: #0f172a;
  --content-card: #1e293b;
  --content-border: #334155;

  --button-secondary: #334155;
  --button-secondary-hover: #475569;
  --button-secondary-text: #cbd5e1;

  --input-bg: #1e293b;
  --input-border: #475569;
  --input-focus: #60a5fa;

  --toast-bg: #1e293b;
  --toast-border: #334155;
  --toast-shadow: rgba(0, 0, 0, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-muted: var(--muted);
  --color-accent: var(--accent);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}

/* Custom Dashboard Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation utilities */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect - theme aware */
.glass {
  background: color-mix(in srgb, var(--background) 25%, transparent);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid color-mix(in srgb, var(--border) 30%, transparent);
}

/* Custom shadows - theme aware */
.shadow-soft {
  box-shadow: var(--toast-shadow);
}

.shadow-colored {
  box-shadow: 0 10px 25px -5px color-mix(in srgb, var(--primary) 25%, transparent),
              0 10px 10px -5px color-mix(in srgb, var(--primary) 4%, transparent);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--toast-shadow);
}

/* Typography improvements */
.text-balance {
  text-wrap: balance;
}

/* Focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom prose styles for blog content */
.prose-custom {
  max-width: none;
  color: #374151;
  line-height: 1.75;
}

.prose-custom h1,
.prose-custom h2,
.prose-custom h3,
.prose-custom h4,
.prose-custom h5,
.prose-custom h6 {
  font-weight: 700;
  color: #111827;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose-custom h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose-custom h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose-custom h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose-custom p {
  color: #374151;
  line-height: 1.75;
  margin-bottom: 1.5rem;
}

.prose-custom a {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
}

.prose-custom a:hover {
  text-decoration: underline;
}

.prose-custom strong {
  color: #111827;
  font-weight: 700;
}

.prose-custom em {
  color: #374151;
  font-style: italic;
}

.prose-custom code {
  color: #ec4899;
  background-color: #fdf2f8;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.prose-custom pre {
  background-color: #111827;
  color: #f9fafb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  overflow-x: auto;
}

.prose-custom blockquote {
  border-left: 4px solid #6366f1;
  background-color: #eef2ff;
  padding: 1.5rem;
  border-radius: 0 0.5rem 0.5rem 0;
  margin: 1.5rem 0;
}

.prose-custom ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.prose-custom ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
}

.prose-custom li {
  color: #374151;
  margin-bottom: 0.5rem;
}

.prose-custom img {
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin: 1.5rem 0;
}

/* Button hover effects */
.btn-hover-scale {
  transition: transform 0.2s ease;
}

.btn-hover-scale:hover {
  transform: scale(1.05);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.bg-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.bg-gradient-pink {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
}

.bg-gradient-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
