# Implementation Plan

- [x] 1. Fix user permission system for ad code insertion





  - Implement proper `canInsertAds` permission checking in user session
  - Update auth configuration to set ad permissions based on user roles (ADMIN, EDITOR)
  - Add permission validation in API endpoints
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 2. Verify and fix ad code database operations
  - Test ad code saving in POST endpoint (`/api/posts`)
  - Test ad code saving in PUT endpoint (`/api/posts/[id]`)
  - Verify ad code retrieval in `getPostBySlug` function
  - Add error handling for postmeta operations
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 3. Create dedicated AdCodeRenderer component
  - Build reusable component for rendering ad codes safely
  - Implement proper HTML sanitization and script execution
  - Add error handling for malformed ad codes
  - Create responsive styling for ad containers
  - _Requirements: 2.1, 2.2, 4.1, 4.2, 4.3_

- [ ] 4. Implement robust script execution system
  - <PERSON>reate ScriptExecutor utility for handling ad scripts
  - Implement execution of inline JavaScript in ad codes
  - Implement loading and execution of external scripts
  - Add timeout handling and error recovery for failed scripts
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 5. Update post view component to use new ad renderer
  - Replace current ad code display logic with AdCodeRenderer component
  - Ensure proper positioning of before/after content ads
  - Add loading states and error handling for ad display
  - Test with various ad code formats (Google AdSense, Facebook, Amazon)
  - _Requirements: 1.4, 2.4, 4.1, 4.2, 4.4_

- [ ] 6. Fix post editor ad code loading for existing posts
  - Ensure ad codes are properly loaded when editing existing posts
  - Update edit post API endpoint to include ad codes in response
  - Test ad code modification and saving in edit mode
  - Verify ad code removal functionality works correctly
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 7. Add comprehensive error handling and validation
  - Implement client-side validation for ad code input
  - Add server-side error handling for database operations
  - Create fallback behavior for failed ad loading
  - Add logging for debugging ad code issues
  - _Requirements: 4.4, 2.4_

- [ ] 8. Create unit tests for ad code functionality
  - Write tests for AdCodeRenderer component
  - Write tests for ScriptExecutor utility
  - Write tests for permission checking logic
  - Write tests for database operations (save/retrieve ad codes)
  - _Requirements: All requirements validation_

- [ ] 9. Perform integration testing with real ad codes
  - Test with Google AdSense ad codes
  - Test with Facebook Audience Network ad codes
  - Test with Amazon Associates ad codes
  - Test with custom HTML/JavaScript ad codes
  - Verify responsive behavior on mobile devices
  - _Requirements: 2.1, 2.2, 4.1, 4.2_

- [ ] 10. Optimize performance and add monitoring
  - Implement lazy loading for ad scripts
  - Add performance monitoring for script execution
  - Optimize database queries for ad code retrieval
  - Add caching for frequently accessed ad codes
  - _Requirements: 2.4, 4.4_