import { db } from '@/lib/db';
import { usermeta } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * Utility functions for managing ads insert permissions
 */

const ADS_PERMISSION_META_KEY = 'can_insert_ads';

/**
 * Check if a user has ads insert permissions
 * @param userId - The user ID to check
 * @returns Promise<boolean> - True if user can insert ads, false otherwise
 */
export async function canUserInsertAds(userId: number): Promise<boolean> {
  try {
    const result = await db
      .select()
      .from(usermeta)
      .where(and(
        eq(usermeta.user_id, userId),
        eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
      ))
      .limit(1);

    if (result.length === 0) {
      // No permission record found, default to false
      return false;
    }

    // Check if the meta_value is '1' or 'true'
    const value = result[0].meta_value;
    return value === '1' || value === 'true';
  } catch (error) {
    console.error('Error checking ads permission:', error);
    return false;
  }
}

/**
 * Grant ads insert permissions to a user
 * @param userId - The user ID to grant permissions to
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export async function grantAdsPermission(userId: number): Promise<boolean> {
  try {
    // Check if permission record already exists
    const existing = await db
      .select()
      .from(usermeta)
      .where(and(
        eq(usermeta.user_id, userId),
        eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
      ))
      .limit(1);

    if (existing.length > 0) {
      // Update existing record
      await db
        .update(usermeta)
        .set({ meta_value: '1' })
        .where(and(
          eq(usermeta.user_id, userId),
          eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
        ));
    } else {
      // Insert new record
      await db.insert(usermeta).values({
        user_id: userId,
        meta_key: ADS_PERMISSION_META_KEY,
        meta_value: '1'
      });
    }

    return true;
  } catch (error) {
    console.error('Error granting ads permission:', error);
    return false;
  }
}

/**
 * Revoke ads insert permissions from a user
 * @param userId - The user ID to revoke permissions from
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export async function revokeAdsPermission(userId: number): Promise<boolean> {
  try {
    // Check if permission record exists
    const existing = await db
      .select()
      .from(usermeta)
      .where(and(
        eq(usermeta.user_id, userId),
        eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
      ))
      .limit(1);

    if (existing.length > 0) {
      // Update existing record to '0'
      await db
        .update(usermeta)
        .set({ meta_value: '0' })
        .where(and(
          eq(usermeta.user_id, userId),
          eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
        ));
    } else {
      // Insert new record with '0' value
      await db.insert(usermeta).values({
        user_id: userId,
        meta_key: ADS_PERMISSION_META_KEY,
        meta_value: '0'
      });
    }

    return true;
  } catch (error) {
    console.error('Error revoking ads permission:', error);
    return false;
  }
}

/**
 * Get ads permissions for multiple users
 * @param userIds - Array of user IDs to check
 * @returns Promise<Record<number, boolean>> - Object mapping user IDs to their permission status
 */
export async function getUsersAdsPermissions(userIds: number[]): Promise<Record<number, boolean>> {
  try {
    if (userIds.length === 0) {
      return {};
    }

    const results = await db
      .select()
      .from(usermeta)
      .where(and(
        eq(usermeta.meta_key, ADS_PERMISSION_META_KEY)
      ));

    const permissions: Record<number, boolean> = {};
    
    // Initialize all users to false
    userIds.forEach(id => {
      permissions[id] = false;
    });

    // Update with actual permissions
    results.forEach(result => {
      const userId = result.user_id;
      if (userIds.includes(userId)) {
        const value = result.meta_value;
        permissions[userId] = value === '1' || value === 'true';
      }
    });

    return permissions;
  } catch (error) {
    console.error('Error getting users ads permissions:', error);
    // Return all false on error
    const permissions: Record<number, boolean> = {};
    userIds.forEach(id => {
      permissions[id] = false;
    });
    return permissions;
  }
}

/**
 * Set ads permission for a user (grant or revoke)
 * @param userId - The user ID
 * @param canInsertAds - True to grant, false to revoke
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export async function setAdsPermission(userId: number, canInsertAds: boolean): Promise<boolean> {
  if (canInsertAds) {
    return await grantAdsPermission(userId);
  } else {
    return await revokeAdsPermission(userId);
  }
}
