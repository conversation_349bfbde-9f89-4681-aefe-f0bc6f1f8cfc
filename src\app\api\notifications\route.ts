import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { notifications } from '@/lib/db/schema';
import { eq, desc, and, count } from 'drizzle-orm';

// GET /api/notifications - Get user's notifications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unread_only') === 'true';
    
    const offset = (page - 1) * limit;

    // Build where condition
    let whereCondition = eq(notifications.user_id, userId);
    if (unreadOnly) {
      whereCondition = and(
        eq(notifications.user_id, userId),
        eq(notifications.is_read, false)
      );
    }

    // Get notifications
    const userNotifications = await db
      .select()
      .from(notifications)
      .where(whereCondition)
      .orderBy(desc(notifications.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(notifications)
      .where(whereCondition);

    // Get unread count
    const [unreadResult] = await db
      .select({ count: count() })
      .from(notifications)
      .where(and(
        eq(notifications.user_id, userId),
        eq(notifications.is_read, false)
      ));

    return NextResponse.json({
      success: true,
      data: {
        notifications: userNotifications,
        pagination: {
          page,
          limit,
          total: totalResult.count,
          hasMore: offset + userNotifications.length < totalResult.count
        },
        unreadCount: unreadResult.count
      }
    });

  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch notifications',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/notifications - Create a new notification (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { user_id, type, title, message, post_id, post_title } = body;

    if (!user_id || !type || !title || !message) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: user_id, type, title, message' },
        { status: 400 }
      );
    }

    const adminId = parseInt(session.user.id);
    const adminName = session.user.name || session.user.username || 'Admin';

    // Create notification
    const result = await db
      .insert(notifications)
      .values({
        user_id: parseInt(user_id),
        type,
        title,
        message,
        post_id: post_id ? parseInt(post_id) : null,
        post_title: post_title || null,
        admin_id: adminId,
        admin_name: adminName,
        is_read: false,
        created_at: new Date(),
      });

    return NextResponse.json({
      success: true,
      data: {
        id: result.insertId,
        message: 'Notification created successfully'
      }
    });

  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create notification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
